
services:
  previa-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: previa-frontend
    ports:
      - "3000:80"
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    networks:
      - previa-network

  # Development service (optional)
  previa-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: base
    container_name: previa-frontend-dev
    ports:
      - "3001:3000"
    volumes:
      - .:/app
      - /app/node_modules
    command: pnpm run dev --host 0.0.0.0
    environment:
      - NODE_ENV=development
    networks:
      - previa-network
    profiles:
      - dev

networks:
  previa-network:
    driver: bridge