NODE_ENV=production
VITE_BASE_API_URL=https://api.previa.vivasoftltd.com/api/v1
VITE_IDENTITY_API_URL=https://api.previa.vivasoftltd.com/identity/api/v1

# ──────────────── Shared Azure OpenAI settings ────────────────
AZURE_OPENAI_KEY=<AZURE_OPENAI_KEY>
AZURE_OPENAI_ENDPOINT=<AZURE_OPENAI_ENDPOINT>

# ─────────────── Whisper (STT) ───────────────
AZURE_OPENAI_STT_DEPLOYMENT_NAME=<WHISPER_DEPLOYMENT_NAME>
AZURE_OPENAI_STT_API_VERSION=<WHISPER_API_VERSION>

# ───────── gpt-4o-mini-tts (TTS) ─────────────
AZURE_OPENAI_TTS_DEPLOYMENT_NAME=<AZURE_OPENAI_TTS_DEPLOYMENT_NAME>
AZURE_OPENAI_TTS_API_VERSION=<AZURE_OPENAI_TTS_API_VERSION>


# ──────────────── STT ────────────────
VITE_PUBLIC_STT_ENDPOINT=<STT_ENDPOINT>


# ──────────────── token key ────────────────
VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY=previa-admin-access-token
VITE_PREVIA_ADMIN_REFRESH_TOKEN_KEY=previa-admin-refresh-token

# ──────────────── UI theme ────────────────
VITE_UI_THEME=previa-ui-theme