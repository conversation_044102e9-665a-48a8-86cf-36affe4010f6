# Previa - AI Interview Platform Frontend

A modern, AI-powered mock interview platform built with React and Vite that helps candidates prepare for job interviews with AI-generated questions, personalized feedback, and interview simulation.

![Previa Landing Page](public/images/previa_landing_page.png)

## 🚀 Features

### Core Functionality

- **AI-Powered Mock Interviews**: Automated interview sessions with intelligent question generation
- **Personalized Feedback**: AI-driven assessment and improvement suggestions
- **Interview Simulation**: Realistic interview environment for better preparation
- **Multi-Role Support**: Tailored questions for different job positions
- **Performance Analytics**: Detailed analysis of interview performance

### Interview Management

- **Dynamic Question Flow**: Adaptive questioning based on candidate responses
- **User Dashboard**: Comprehensive interview history and analytics
- **Settings Management**: Customizable user preferences and appearance

### Technical Features

- **Light/Dark Mode**: Theme switching with system preference detection
- **Built-in Sidebar**: Responsive navigation component
- **Authentication System**: Secure user authentication with token management

## 🛠️ Tech Stack

- **Framework**: React 19 with Vite
- **Language**: TypeScript
- **Routing**: TanStack Router
- **Styling**: Tailwind CSS
- **UI Components**: ShadcnUI (Radix UI primitives)
- **State Management**: Zustand
- **Icons**: Tabler Icons
- **Build Tool**: Vite
- **Linting/Formatting**: ESLint & Prettier

## 📁 Project Structure

```
src/
├── components/             # Reusable UI components
│   ├── ui/                # Base UI components (ShadcnUI)
│   └── shared/            # Shared components across features
├── config/                # Configuration files
│   └── fonts.ts          # Font configuration
├── context/               # React context providers
│   └── font-context.tsx  # Font theme context
├── routes/                # TanStack Router routes
│   ├── _authenticated/   # Protected routes
│   │   ├── apps/         # Applications page
│   │   ├── chats/        # Chat interface
│   │   ├── help-center/  # Help and support
│   │   ├── settings/     # User settings
│   │   ├── tasks/        # Task management
│   │   └── users/        # User management
│   ├── (auth)/           # Authentication routes
│   │   ├── sign-in/      # Login pages
│   │   ├── sign-up/      # Registration
│   │   ├── otp/          # OTP verification
│   │   └── forgot-password/ # Password recovery
│   └── (errors)/         # Error pages (401, 403, 404, 500, 503)
├── stores/                # Zustand stores
│   └── authStore.ts      # Authentication state management
├── hooks/                 # Custom React hooks
├── utils/                 # Utility functions and helpers
├── types/                 # TypeScript type definitions
└── routeTree.gen.ts      # Auto-generated route tree
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended), npm, or yarn
- Git access to Gitea repository

### Installation

1. **Clone the repository**

   ```bash
   git clone https://gitea.vivasoftltd.com/Vivasoft/previa-v1-frontend.git
   cd previa-v1-frontend
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   # or
   npm install
   # or
   yarn install
   ```

3. **Environment Setup**
   Create a `.env.local` file in the root directory:

   ```env
   NODE_ENV=production
   VITE_API_URL=your_backend_api_url
   VITE_APP_NAME=Previa
   VITE_APP_VERSION=1.0.0
   VITE_IDENTITY_API_URL=your_identity_api_url
   AZURE_OPENAI_KEY=<AZURE_OPENAI_KEY>
   AZURE_OPENAI_ENDPOINT=<AZURE_OPENAI_ENDPOINT>
   ```

4. **Run the development server**

   ```bash
   pnpm run dev
   # or
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Key Components

### Authentication System

- **AuthStore**: Zustand-based authentication state management
- **Protected Routes**: Route-level authentication guards
- **Token Management**: Secure token storage with cookies
- **Multi-role Support**: Role-based access control

### Interview Management

- **InterviewStore**: State management for interview-related data
- **Question Generation**: AI-powered question generation
- **Feedback Generation**: AI-driven feedback generation
- **Interview Simulation**: Realistic interview environment

## 🔧 Development

### Code Quality

- **ESLint**: Code linting with TypeScript support
- **Prettier**: Code formatting and style consistency
- **TypeScript**: Full type safety and developer experience
- **Vite**: Fast development server and optimized builds

### Component Development

- Components follow atomic design principles
- ShadcnUI for consistent design system
- Custom hooks for reusable logic
- Context providers for global state

## 🤝 Development

### Route Management

- Routes are automatically generated by TanStack Router
- Use file-based routing in `src/routes/`
- Protected routes go under `_authenticated/`
- Error pages in `(errors)/` directory

### State Management

- Use Zustand for global state
- Local storage integration for persistence

### Development Guidelines

- Follow TypeScript best practices
- Write meaningful commit messages
- Add proper type definitions
- Test components thoroughly
- Follow the existing code structure

## 📄 License

This project is proprietary software developed by Vivasoft Ltd.

---

**Crafted with 🤍🩵 by Vivasoft Ltd**
