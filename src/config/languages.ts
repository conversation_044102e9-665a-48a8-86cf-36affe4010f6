/**
 * List of available language names (visit the url`/settings/appearance`).
 * This array is used to generate language options in the appearance form.
 *
 * 📝 How to Add a New Language:
 * 1. Add the language code here.
 * 2. Add translation files for the new language.
 * 3. Update any i18n configuration if using internationalization.
 *
 * Example:
 * languages.ts → Add 'es' to this array for Spanish.
 */
export const languages = ['english'] as const;
