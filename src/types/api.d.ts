interface IResponseData<T> {
  data: T;
  message?: string;
  success: boolean;
  status: number;
}

interface IResponseData<T> {
  data: T;
  message?: string;
  success: boolean;
  status: number;
}

// Auth types
interface ILoginRequest {
  email: string;
  password: string;
}

interface ILoginResponse {
  message: string;
  data: {
    access_token: string;
    refresh_token: string;
    user: IUser;
  };
}

interface IRegisterRequest {
  name: string;
  email: string;
  password: string;
}

// interface IUser {
//   id: string;
//   name?: string;
//   email: string;
//   user_name: string;
//   is_active?: boolean;
//   role?: string;
//   avatar?: string;
// }

interface IUserResponse {
  data: {
    user: IUser;
  };
}
