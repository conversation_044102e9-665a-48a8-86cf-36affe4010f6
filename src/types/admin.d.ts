
interface IUser {
  id: number;
  name?: string;
  email: string;
  user_name: string;
  is_active: boolean;
}

interface AdminLoginPayload {
  email: string;
  password: string;
}

interface AdminLoginResponse {
  access_token: string;
  refresh_token: string;
  user: IUser;
}
interface DashboardState {
  selectedJob: DashboardJob | null;
  selectedCandidate: CandidateList | null;
}

interface DashboardActions {
  setSelectedJob: (job: DashboardJob | null) => void;
  clearSelectedJob: () => void;
  setSelectedCandidate: (candidate: CandidateList | null) => void;
  clearSelectedCandidate: () => void;
}

type DashboardSlice = DashboardState & DashboardActions;
