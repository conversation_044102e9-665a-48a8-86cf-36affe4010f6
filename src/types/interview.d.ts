interface CandidateList {
  id: number;
  email: string;
  position_applied_for: string;
  full_name: string;
  name?: string;
  phone_number: string;
  address: string;
  skills: string[];
  job_id: number;
  years_of_experience: string;
  status: CandidateInterviewStatus;
  avg_score: number;
  avatar?: string;
  cv_link?: string;
  interview_date?: string;
  eligibility_info?: ElegiblityData;
}

interface DashboardJob {
  id: number;
  title: string;
  location: string;
  min_exp: number;
  max_exp: number;
  is_active: boolean;
  application_deadline: string;
  candidate_count: {
    total: number;
    attended: number;
    not_attended: number;
  };
}
