import Cookies from 'js-cookie';

export default class ClientSideCookieManager {
  static setClientCookie(name: string, value: string, options?: Cookies.CookieAttributes) {
    Cookies.set(name, value, {
      ...options,
      secure: options?.secure ?? process.env.NODE_ENV === 'production',
    });
  }

  static getClientCookie(name: string): string | undefined {
    return Cookies.get(name);
  }

  static deleteClientCookie(name: string) {
    Cookies.remove(name);
  }

  static isAuthenticatedClientSide() {
    const accessToken = Cookies.get(import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!);
    return !!accessToken;
  }

  static login(accessToken: string, refreshToken: string) {
    Cookies.set(import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!, accessToken);
    Cookies.set(import.meta.env.VITE_PREVIA_ADMIN_REFRESH_TOKEN_KEY!, refreshToken);
  }

  static logout() {
    Cookies.remove(import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!);
    Cookies.remove(import.meta.env.VITE_PREVIA_ADMIN_REFRESH_TOKEN_KEY!);
  }
}
