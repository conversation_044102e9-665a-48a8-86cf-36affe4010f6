import { languages } from '@/config/languages';
import { useStore } from '@/stores/store';
import { useEffect } from 'react';

export const useLanguage = () => {
  const language = useStore((state) => state.language);
  const setLanguage = useStore((state) => state.setLanguage);

  useEffect(() => {
    // Initialize language from localStorage on mount
    const savedLanguage = localStorage.getItem('language');
    const initialLanguage = languages.includes(savedLanguage as Language)
      ? (savedLanguage as Language)
      : languages[0];

    if (language !== initialLanguage) {
      setLanguage(initialLanguage);
    }

    // Apply initial language class
    const root = document.documentElement;
    root.classList.forEach((cls) => {
      if (cls.startsWith('lang-')) root.classList.remove(cls);
    });
    root.classList.add(`lang-${language}`);
  }, [language, setLanguage]);

  return {
    language,
    setLanguage,
  };
};
