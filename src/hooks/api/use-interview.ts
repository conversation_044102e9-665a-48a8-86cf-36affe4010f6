import {
  generateQuestion,
  submitAnswer,
  createInterviewSession,
  QuestionRequest,
  AnswerSubmissionRequest,
  InterviewCreationRequest,
} from '@/services/interview';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const interviewKeys = {
  all: ['interview'] as const,
  sessions: () => [...interviewKeys.all, 'session'] as const,
  session: (id: string) => [...interviewKeys.sessions(), id] as const,
  questions: () => [...interviewKeys.all, 'question'] as const,
  question: (sessionId: string, questionIndex: number) =>
    [...interviewKeys.questions(), sessionId, questionIndex] as const,
};

// Generate Question Mutation
export function useGenerateQuestionMutation() {
  return useMutation({
    mutationKey: ['generate-question'],
    mutationFn: async (payload: QuestionRequest) => {
      const response = await generateQuestion(payload);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to generate question';
        toast.error(errorMessage);
        console.error('Question generation failed:', error);
      }
    },
  });
}

// Submit Answer Mutation
export function useSubmitAnswerMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['submit-answer'],
    mutationFn: async (payload: AnswerSubmissionRequest) => {
      const response = await submitAnswer(payload);
      return response.data;
    },
    onSuccess: (data, variables) => {
      console.log('Answer submitted successfully:', data);
      // Invalidate related queries if needed
      queryClient.invalidateQueries({
        queryKey: interviewKeys.session(variables.interviewId),
      });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to submit answer';
        toast.error(errorMessage);
        console.error('Answer submission failed:', error);
      }
    },
  });
}

// Create Interview Session Mutation
export function useCreateInterviewSessionMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['create-interview-session'],
    mutationFn: async (payload: InterviewCreationRequest) => {
      const response = await createInterviewSession(payload);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(interviewKeys.session(data.interviewId), data);
      toast.success('Interview session created successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create interview session';
        toast.error(errorMessage);
      }
    },
  });
}

// Optional: Query for interview session details (if needed)
export function useInterviewSessionQuery(interviewId: string, options = { enabled: true }) {
  return useQuery({
    queryKey: interviewKeys.session(interviewId),
    queryFn: async () => {
      // If you have an endpoint to get interview session details
      // const response = await getInterviewSession(interviewId);
      // return response.data;
      return null; // Placeholder
    },
    enabled: options.enabled && !!interviewId,
    staleTime: 5 * 60 * 1000,
  });
}
