import ClientSideCookieManager from '@/lib/cookie';
import { useStore } from '@/stores/store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';

export const useAuthSlice = () => {
  const { auth, setAccessToken, setRefreshToken, setAuth, setUser, updateUser, clearAuth } =
    useStore(
      useShallow((state) => ({
        auth: state,
        setAccessToken: state.setAccessToken,
        setRefreshToken: state.setRefreshToken,
        setAuth: state.setAuth,
        setUser: state.setUser,
        updateUser: state.updateUser,
        clearAuth: state.clearAuth,
      }))
    );

  useEffect(() => {
    // Initialize auth from cookies on mount
    const accessToken = ClientSideCookieManager.getClientCookie(
      import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!
    );
    const refreshToken = ClientSideCookieManager.getClientCookie(
      import.meta.env.VITE_PREVIA_ADMIN_REFRESH_TOKEN_KEY!
    );

    if (accessToken && refreshToken) {
      setAccessToken(accessToken);
      setRefreshToken(refreshToken);
    }
  }, [setAccessToken, setRefreshToken]);

  const login = (accessToken: string, refreshToken: string, user?: AuthState['user']) => {
    // Set tokens in cookies
    ClientSideCookieManager.login(accessToken, refreshToken);

    // Set tokens in store
    setAccessToken(accessToken);
    setRefreshToken(refreshToken);

    // Set user if provided
    if (user) {
      setUser(user);
    }
  };

  const logout = () => {
    // Clear cookies
    ClientSideCookieManager.logout();

    // Clear store
    clearAuth();
  };

  const setTokens = (accessToken: string, refreshToken: string) => {
    // Update cookies
    ClientSideCookieManager.login(accessToken, refreshToken);

    // Update store
    setAccessToken(accessToken);
    setRefreshToken(refreshToken);
  };

  const isAuthenticated = () => {
    return !!auth.access_token && ClientSideCookieManager.isAuthenticatedClientSide();
  };

  // if consumers re-render unnecessarily, use the useMemo in return
  return {
    // State
    auth,
    user: auth.user,
    accessToken: auth.access_token,
    refreshToken: auth.refresh_token,

    // Actions
    setAuth,
    setUser,
    updateUser,
    setTokens,
    login,
    logout,
    isAuthenticated,
  };
};
