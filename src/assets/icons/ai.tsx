export const AIIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  const { height = '60', width = '60', ...rest } = props;

  return (
    <svg
      width={width}
      height={height}
      viewBox='0 0 60 60'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...rest}
    >
      <rect width='60' height='60' rx='30' fill='url(#paint0_linear_1463_18881)' />
      <path
        d='M28.1274 14C28.4033 21.685 34.5695 27.8512 42.2544 28.127C34.5695 28.4028 28.4033 34.569 28.1274 42.254C27.8516 34.569 21.6854 28.4028 14.0005 28.127C21.6854 27.8512 27.8516 21.685 28.1274 14Z'
        fill='white'
      />
      <path
        d='M38.9365 31.8728C39.0744 35.7153 42.1575 38.7984 46 38.9363C42.1575 39.0742 39.0744 42.1573 38.9365 45.9998C38.7986 42.1573 35.7155 39.0742 31.873 38.9363C35.7155 38.7984 38.7986 35.7153 38.9365 31.8728Z'
        fill='white'
      />
      <defs>
        <linearGradient
          id='paint0_linear_1463_18881'
          x1='30'
          y1='-6.5625'
          x2='77.8125'
          y2='54.375'
          gradientUnits='userSpaceOnUse'
        >
          <stop offset='0.210966' stopColor='#5C92FA' />
          <stop offset='1' stopColor='#A75FFD' />
        </linearGradient>
      </defs>
    </svg>
  );
};
