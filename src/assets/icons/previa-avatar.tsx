import * as React from "react";

function PreviaAvatarIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 60 60" fill="none" {...props}>
      <rect
        width={60}
        height={60}
        rx={30}
        fill="url(#prefix__paint0_linear_1460_12482)"
      />
      <path
        d="M28.127 14c.276 7.685 6.442 13.851 14.127 14.127-7.684.276-13.85 6.442-14.127 14.127-.275-7.685-6.442-13.851-14.126-14.127 7.684-.276 13.85-6.442 14.126-14.127zM38.937 31.873A7.331 7.331 0 0046 38.937 7.331 7.331 0 0038.937 46a7.331 7.331 0 00-7.064-7.063 7.331 7.331 0 007.064-7.064z"
        fill="#fff"
      />
      <defs>
        <linearGradient
          id="prefix__paint0_linear_1460_12482"
          x1={30}
          y1={-6.563}
          x2={77.813}
          y2={54.375}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.211} stopColor="#5C92FA" />
          <stop offset={1} stopColor="#A75FFD" />
        </linearGradient>
      </defs>
    </svg>
  );
}

const PreviaAvatar = React.memo(PreviaAvatarIcon);
export default PreviaAvatar;
