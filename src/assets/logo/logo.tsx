export const PreviaLogo: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  const { fill, height = '32', width = '32', ...rest } = props;

  return (
    <svg
      width={width}
      height={height}
      viewBox='0 0 32 32'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...rest}
    >
      <path
        d='M0 0C9.01627 8.39148 22.9837 8.39148 32 0C23.6085 9.01627 23.6085 22.9837 32 32C22.9837 23.6085 9.01627 23.6085 0 32C8.39148 22.9837 8.39148 9.01627 0 0Z'
        fill='url(#paint0_linear_1446_18633)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_1446_18633'
          x1='16'
          y1='-3.5'
          x2='41.5'
          y2='29'
          gradientUnits='userSpaceOnUse'
        >
          <stop offset='0.210966' stopColor='#5C92FA' />
          <stop offset='1' stopColor='#A75FFD' />
        </linearGradient>
      </defs>
    </svg>
  );
};
