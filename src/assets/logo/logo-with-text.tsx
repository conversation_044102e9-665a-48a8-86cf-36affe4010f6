export const LogoWithText: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  const { height = '32', width = '95', ...rest } = props;

  return (
    <svg
      width={width}
      height={height}
      viewBox='0 0 95 32'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...rest}
    >
      <path
        d='M0 0C9.01627 8.39148 22.9837 8.39148 32 0C23.6085 9.01627 23.6085 22.9837 32 32C22.9837 23.6085 9.01627 23.6085 0 32C8.39148 22.9837 8.39148 9.01627 0 0Z'
        fill='url(#paint0_linear_1445_11126)'
      />
      <path
        d='M92.5864 12.8645H94.9823V23.7395H92.5864V22.177C91.6281 23.3923 90.3573 24 88.7739 24C87.767 24 86.8538 23.7534 86.0344 23.2604C85.2149 22.7604 84.5726 22.0763 84.1073 21.2083C83.649 20.3402 83.4198 19.3715 83.4198 18.302C83.4198 17.2326 83.649 16.2638 84.1073 15.3958C84.5726 14.5277 85.2149 13.8472 86.0344 13.3541C86.8538 12.8541 87.767 12.6041 88.7739 12.6041C90.3642 12.6041 91.6351 13.2083 92.5864 14.4166V12.8645ZM89.2427 21.6875C90.201 21.6875 90.9962 21.3645 91.6281 20.7187C92.267 20.0659 92.5864 19.2604 92.5864 18.302C92.5864 17.3368 92.267 16.5312 91.6281 15.8854C90.9962 15.2326 90.201 14.9062 89.2427 14.9062C88.2774 14.9062 87.4719 15.2326 86.826 15.8854C86.1871 16.5312 85.8677 17.3368 85.8677 18.302C85.8677 19.2604 86.1871 20.0659 86.826 20.7187C87.4719 21.3645 88.2774 21.6875 89.2427 21.6875Z'
        fill='#323232'
      />
      <path
        d='M80.8573 11.0208C80.4267 11.0208 80.0656 10.875 79.774 10.5833C79.4823 10.2917 79.3365 9.93403 79.3365 9.51042C79.3365 9.07292 79.4823 8.71181 79.774 8.42708C80.0656 8.14236 80.4267 8 80.8573 8C81.2948 8 81.6594 8.14236 81.951 8.42708C82.2427 8.71181 82.3885 9.07292 82.3885 9.51042C82.3885 9.94097 82.2427 10.3021 81.951 10.5938C81.6594 10.8785 81.2948 11.0208 80.8573 11.0208ZM79.6594 23.7396V12.8646H82.0552V23.7396H79.6594Z'
        fill='#323232'
      />
      <path
        d='M76.2412 12.8646H78.9079L74.2725 23.7396H71.8037L67.1683 12.8646H69.9079L73.0641 20.7917L76.2412 12.8646Z'
        fill='#323232'
      />
      <path
        d='M67.7829 18.3229C67.7829 18.6423 67.762 18.9548 67.7204 19.2604H58.9079C59.1023 20.0451 59.512 20.6736 60.137 21.1458C60.769 21.6111 61.5259 21.8437 62.4079 21.8437C63.0884 21.8437 63.7204 21.7013 64.3037 21.4166C64.894 21.1319 65.3523 20.7743 65.6787 20.3437L67.2933 21.7291C66.7308 22.4375 66.0155 22.993 65.1475 23.3958C64.2863 23.7986 63.3523 24 62.3454 24C60.6232 24 59.1891 23.4513 58.0433 22.3541C56.9044 21.2569 56.335 19.8923 56.335 18.2604C56.335 17.2048 56.585 16.2465 57.085 15.3854C57.585 14.5173 58.2725 13.8368 59.1475 13.3437C60.0294 12.8507 61.0051 12.6041 62.0745 12.6041C63.7065 12.6041 65.0641 13.1493 66.1475 14.2395C67.2377 15.3229 67.7829 16.684 67.7829 18.3229ZM62.0537 14.7604C61.2829 14.7604 60.6093 14.993 60.0329 15.4583C59.4634 15.9236 59.0815 16.5382 58.887 17.302H65.2308C65.0502 16.5382 64.6683 15.9236 64.085 15.4583C63.5086 14.993 62.8315 14.7604 62.0537 14.7604Z'
        fill='#323232'
      />
      <path
        d='M56.0418 12.6875H56.4481V15.1458H56.0418C54.9863 15.1458 54.1564 15.4444 53.5522 16.0417C52.955 16.6319 52.6564 17.434 52.6564 18.4479V23.7396H50.2606V12.8646H52.6564V14.4583C53.5244 13.2778 54.6529 12.6875 56.0418 12.6875Z'
        fill='#323232'
      />
      <path
        d='M44.2501 9.02087C45.1668 9.02087 46.0001 9.22921 46.7501 9.64587C47.5071 10.0556 48.1008 10.6285 48.5314 11.3646C48.9619 12.1007 49.1772 12.9202 49.1772 13.823C49.1772 15.1841 48.705 16.3195 47.7606 17.2292C46.8161 18.1389 45.646 18.5938 44.2501 18.5938H40.5314V23.7396H38.0001V9.02087H44.2501ZM44.2085 16.2084C44.8821 16.2084 45.4446 15.9827 45.896 15.5313C46.3474 15.073 46.5731 14.5035 46.5731 13.823C46.5731 13.1285 46.3474 12.5521 45.896 12.0938C45.4446 11.6355 44.8821 11.4063 44.2085 11.4063H40.5314V16.2084H44.2085Z'
        fill='#323232'
      />
      <defs>
        <linearGradient
          id='paint0_linear_1445_11126'
          x1='16'
          y1='-3.5'
          x2='41.5'
          y2='29'
          gradientUnits='userSpaceOnUse'
        >
          <stop offset='0.210966' stopColor='#5C92FA' />
          <stop offset='1' stopColor='#A75FFD' />
        </linearGradient>
      </defs>
    </svg>
  );
};
