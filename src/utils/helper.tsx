export function getCapitalizedText(text: string): string {
  if (!text) return text;
  return text?.charAt(0)?.toUpperCase() + text?.slice(1).toLowerCase();
}

export const getValuesFromOptions = (options: SelectProp[]) => {
  if (!options || options.length === 0) return [];

  return options.map((option: SelectProp) => option.value);
};

export  const getFormatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };