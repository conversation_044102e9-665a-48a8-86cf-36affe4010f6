export const roles = [
  {
    label: 'Software Engineer',
    value: 'Software Engineer',
  },
  {
    label: 'Product Manager',
    value: 'Product Manager',
  },
  {
    label: 'Data Scientist',
    value: 'Data Scientist',
  },
  {
    label: 'UX Designer',
    value: 'UX Designer',
  },
  {
    label: 'DevOps Engineer',
    value: 'DevPps Engineer',
  },
  {
    label: 'Marketing Specialist',
    value: 'Marketing Specialist',
  },
  {
    label: 'Sales Representative',
    value: 'Sales Representative',
  },
];

export const skills = [
  {
    value: 'javascript',
    label: 'JavaScript',
  },
  {
    value: 'react',
    label: 'React',
  },
  {
    value: 'nodejs',
    label: 'Node.js',
  },
  {
    value: 'python',
    label: 'Python',
  },
  {
    value: 'java',
    label: 'Java',
  },
  {
    value: 'csharp',
    label: 'C#',
  },
  {
    value: 'sql',
    label: 'SQL',
  },
  {
    value: 'aws',
    label: 'AWS',
  },
  {
    value: 'docker',
    label: 'Docker',
  },
  {
    value: 'kubernetes',
    label: 'Kubernetes',
  },
  {
    value: 'machine_learning',
    label: 'Machine Learning',
  },
  {
    value: 'ui_ux_design',
    label: 'UI/UX Design',
  },
  {
    value: 'project_management',
    label: 'Project Management',
  },
  {
    value: 'agile_methodologies',
    label: 'Agile Methodologies',
  },
  {
    value: 'data_analysis',
    label: 'Data Analysis',
  },
  {
    value: 'typescript',
    label: 'TypeScript',
  },
  {
    value: 'graphql',
    label: 'GraphQL',
  },
  {
    value: 'django',
    label: 'Django',
  },
  {
    value: 'flask',
    label: 'Flask',
  },
  {
    value: 'spring_boot',
    label: 'Spring Boot',
  },
  {
    value: 'azure',
    label: 'Azure',
  },
  {
    value: 'google_cloud',
    label: 'Google Cloud',
  },
  {
    value: 'devops',
    label: 'DevOps',
  },
  {
    value: 'ci_cd',
    label: 'CI/CD',
  },
  {
    value: 'html',
    label: 'HTML',
  },
  {
    value: 'css',
    label: 'CSS',
  },
  {
    value: 'sass',
    label: 'SASS',
  },
  {
    value: 'tailwind_css',
    label: 'Tailwind CSS',
  },
  {
    value: 'bootstrap',
    label: 'Bootstrap',
  },
  {
    value: 'vuejs',
    label: 'Vue.js',
  },
  {
    value: 'angular',
    label: 'Angular',
  },
  {
    value: 'figma',
    label: 'Figma',
  },
  {
    value: 'mysql',
    label: 'MySQL',
  },
  {
    value: 'postgresql',
    label: 'PostgreSQL',
  },
  {
    value: 'mongodb',
    label: 'MongoDB',
  },
  {
    value: 'firebase',
    label: 'Firebase',
  },
  {
    value: 'sqlite',
    label: 'SQLite',
  },
  {
    value: 'oracle',
    label: 'Oracle Database',
  },
  {
    value: 'redis',
    label: 'Redis',
  },
  {
    value: 'android',
    label: 'Android Development',
  },
  {
    value: 'kotlin',
    label: 'Kotlin',
  },
  {
    value: 'java_android',
    label: 'Java (Android)',
  },
  {
    value: 'flutter',
    label: 'Flutter',
  },
  {
    value: 'react_native',
    label: 'React Native',
  },
  {
    value: 'android_studio',
    label: 'Android Studio',
  },
  {
    value: 'gradle',
    label: 'Gradle',
  },
  {
    value: 'terraform',
    label: 'Terraform',
  },
  {
    value: 'ansible',
    label: 'Ansible',
  },
  {
    value: 'puppet',
    label: 'Puppet',
  },
  {
    value: 'jenkins',
    label: 'Jenkins',
  },
  {
    value: 'circleci',
    label: 'CircleCI',
  },
  {
    value: 'git',
    label: 'Git',
  },
  {
    value: 'github',
    label: 'GitHub',
  },
  {
    value: 'gitlab',
    label: 'GitLab',
  },
  {
    value: 'prometheus',
    label: 'Prometheus',
  },
  {
    value: 'grafana',
    label: 'Grafana',
  },
  {
    value: 'elk_stack',
    label: 'ELK Stack',
  },
  {
    value: 'monitoring',
    label: 'Monitoring & Logging',
  },
  {
    value: 'scripting',
    label: 'Scripting (Bash, PowerShell)',
  },
];
