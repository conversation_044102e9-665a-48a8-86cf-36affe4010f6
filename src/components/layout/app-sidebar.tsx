'use client';

import { SidebarHeader as CustomSidebarHeader } from '@/components/shared/sidebar-header';
import { SidebarNavigation } from '@/components/shared/sidebar-navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail,
  useSidebar,
  SIDEBAR_COOKIE_NAME,
} from '@/components/ui/sidebar';
import ClientSideCookieManager from '@/lib/cookie';
import { motion } from 'framer-motion';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { state, toggleSidebar } = useSidebar();
  const isCollapsed = state === 'collapsed';

  const handleToggleSidebar = () => {
    toggleSidebar();
    // Update cookie state
    const newState = state === 'collapsed' ? 'expanded' : 'collapsed';
    ClientSideCookieManager.setClientCookie(SIDEBAR_COOKIE_NAME, newState);
  };

  return (
    <motion.div
      initial={false}
      transition={{
        duration: 0.3,
        ease: 'easeInOut',
      }}
    >
      <Sidebar collapsible='icon' {...props}>
        <SidebarHeader className='p-0'>
          <CustomSidebarHeader isCollapsed={isCollapsed} onToggle={handleToggleSidebar} />
        </SidebarHeader>

        <SidebarContent>
          <SidebarNavigation isCollapsed={isCollapsed} />
        </SidebarContent>

        <SidebarRail />
      </Sidebar>
    </motion.div>
  );
}
