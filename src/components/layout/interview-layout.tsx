import { CrossIcon, LogoWithText } from '@/assets';
import { ReactNode } from 'react';

interface InterviewLayoutProps {
  children: ReactNode;
  jobTitle?: string;
}

const InterviewLayout = ({ children, jobTitle }: InterviewLayoutProps) => {
  return (
    <>
      <div className='flex items-center justify-between bg-white px-6 py-5'>
        <div className='flex items-center'>
          <LogoWithText height='32' width='95' />
        </div>
        {jobTitle && (
          <h1 className='text-xl font-semibold text-gray-900'>Interview for {jobTitle}</h1>
        )}
        <div>
          <div className='flex items-center justify-center'>
            <CrossIcon />
            <span className='text-sm text-neutral-600'>Exit Interview</span>
          </div>
        </div>
      </div>
      {children}
    </>
  );
};
export default InterviewLayout;
