'use client';

import { ProfileDropdown } from './profile-dropdown';
import { Button } from '@/components/ui/button';
import { Link } from '@tanstack/react-router';
import { useNavigate } from '@tanstack/react-router';
import { ChevronLeft } from 'lucide-react';
import React from 'react';

interface BreadcrumbItem {
  label: string;
  href?: string;
}
interface PageHeaderProps {
  title: string;
  userName?: string;
  userAvatar?: string;
  showNavigation?: boolean;
  breadcrumbs?: BreadcrumbItem[];
}

export function PageHeader({
  title,
  userName = '<PERSON> Jones',
  userAvatar,
  showNavigation = false,
  breadcrumbs = [],
}: PageHeaderProps) {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate({ to: '..' });
  };

  return (
    <div className='flex items-center justify-between p-6 py-5'>
      {showNavigation ? (
        <div className='flex items-center gap-3'>
          <Button variant='ghost' size='icon' onClick={handleBack}>
            <ChevronLeft className='size-4' />
          </Button>

          <nav className='text-muted-foreground flex items-center space-x-2 text-sm'>
            {breadcrumbs.map((breadcrumb, index) => (
              <React.Fragment key={`${breadcrumb.label}`}>
                {breadcrumb.href ? (
                  <Link to={breadcrumb.href} className='hover:text-foreground'>
                    {breadcrumb.label}
                  </Link>
                ) : (
                  <span className={index === breadcrumbs.length - 1 ? 'text-foreground' : ''}>
                    {breadcrumb.label}
                  </span>
                )}
                {index !== breadcrumbs.length - 1 && <span>/</span>}
              </React.Fragment>
            ))}
          </nav>
        </div>
      ) : (
        <h1 className='text-2xl font-normal text-gray-700 dark:text-white'>{title}</h1>
      )}

      <ProfileDropdown userName={userName} userAvatar={userAvatar ?? ''} />
    </div>
  );
}
