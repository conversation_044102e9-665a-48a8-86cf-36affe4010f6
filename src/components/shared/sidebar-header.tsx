'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { SidebarHeaderProps } from '@/types/sidebar';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';

export function SidebarHeader({ isCollapsed, onToggle }: SidebarHeaderProps) {
  return (
    <div className='flex h-16 items-center justify-between border-b px-4'>
      <div className='flex items-center gap-2'>
        {/* Logo always visible */}
        <img
          src='/images/previa_logo.png'
          alt='Logo'
          className={`${isCollapsed ? 'size-6' : 'size-8'} shrink-0`}
          width={28}
          height={28}
        />
        {/* Text only when expanded */}
        <motion.span
          initial={false}
          animate={{
            opacity: isCollapsed ? 0 : 1,
            width: isCollapsed ? 0 : 'auto',
          }}
          transition={{ duration: 0.2 }}
          className='overflow-hidden text-lg font-semibold whitespace-nowrap'
        >
          Previa
        </motion.span>
      </div>

      <Button
        variant='ghost'
        size='icon'
        onClick={onToggle}
        className={cn(isCollapsed ? 'size-5' : 'size-8')}
        aria-label='Toggle sidebar'
        title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
      >
        {isCollapsed ? <ChevronRight className='size-4' /> : <ChevronLeft className='size-4' />}
      </Button>
    </div>
  );
}
