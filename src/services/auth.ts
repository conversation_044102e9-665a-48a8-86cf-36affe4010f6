import { IdentityServiceInstance } from './index';
import { API_ROUTES } from '@/utils/constants';

const { LOGIN, REFRESH_TOKEN } = API_ROUTES.AUTH;
const { PROFILE, UPDATE_PROFILE } = API_ROUTES.USER;

export const login = async (payload: ILoginRequest): Promise<IResponseData<ILoginResponse>> => {
  return IdentityServiceInstance.callPostApi<ILoginResponse, ILoginRequest>(LOGIN, payload);
};

export const refreshTokens = async (
  refresh_token: string
): Promise<IResponseData<ILoginResponse>> => {
  return IdentityServiceInstance.callPostApi<ILoginResponse, { refresh_token: string }>(
    REFRESH_TOKEN,
    { refresh_token }
  );
};

// export const register = async (
//   payload: IRegisterRequest
// ): Promise<IResponseData<IUser>> => {
//   return IdentityServiceInstance.callPostApi<IUser, IRegisterRequest>(
//     API_ROUTES.AUTH.REGISTER,
//     payload
//   )
// }
export const getUserProfile = async (): Promise<IResponseData<IUserResponse>> => {
  return IdentityServiceInstance.callGetApi<IUserResponse>(PROFILE);
};

export const updateUserProfile = async (payload: Partial<IUser>): Promise<IResponseData<IUser>> => {
  return IdentityServiceInstance.callPutApi<IUser, Partial<IUser>>(UPDATE_PROFILE, payload);
};
