import { ApiServiceInstance } from '.';
import { API_ROUTES } from '@/utils/constants';

const { LIVE_PREVIEW, LIVE_EVALUATION } = API_ROUTES.PROMPT;
const { CREATE_USER_INTERVIEW, SUBMIT_FEEDBACK } = API_ROUTES.INTERVIEW;

export interface QuestionRequest {
  role: string;
  session_id?: string | undefined;
  cv_s3_key?: string | undefined;
  difficulty: string;
  prompt_text: string;
  qa_array: {
    question: string;
    answer: string;
  }[];
  remaining_time?: number;
}

export interface QuestionResponse {
  data: string; // The question text itself
}

export interface AnswerSubmissionRequest {
  interviewId: string;
  questionId: string;
  answer: string;
  duration: number;
  timestamp: string;
}

export interface InterviewCreationRequest {
  candidateProfile: {
    name: string;
    position: string;
    company: string;
  };
  totalDuration: number;
}

export interface InterviewCreationResponse {
  interviewId: string;
  totalDuration: number;
  candidateProfile: {
    name: string;
    position: string;
    company: string;
  };
}

// Generate next question
export const generateQuestion = async (
  payload: QuestionRequest
): Promise<IResponseData<QuestionResponse>> => {
  return ApiServiceInstance.callPostApi<QuestionResponse, QuestionRequest>(LIVE_PREVIEW, payload);
};

// Submit answer
export const submitAnswer = async (
  payload: AnswerSubmissionRequest
): Promise<IResponseData<any>> => {
  return ApiServiceInstance.callPostApi<any, AnswerSubmissionRequest>(LIVE_EVALUATION, payload);
};

// Create interview session
export const createInterviewSession = async (
  payload: InterviewCreationRequest
): Promise<IResponseData<InterviewCreationResponse>> => {
  return ApiServiceInstance.callPostApi<InterviewCreationResponse, InterviewCreationRequest>(
    CREATE_USER_INTERVIEW,
    payload
  );
};
