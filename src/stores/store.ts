import { createDashboardSlice } from './slices/dashboardSlice';
import { createAuthSlice } from '@/stores/slices/authSlice';
import { createJobSlice } from '@/stores/slices/jobSlice';
import { createLanguageSlice } from '@/stores/slices/languageSlice';
import { createThemeSlice } from '@/stores/slices/themeSlice';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export const useStore = create<Store>()(
  devtools(
    immer((...a) => ({
      ...createAuthSlice(...a),
      ...createThemeSlice(...a),
      ...createLanguageSlice(...a),
      ...createDashboardSlice(...a),
      ...createJobSlice(...a),
    })),
    {
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);
