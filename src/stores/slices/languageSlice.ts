import { type StateCreator } from 'zustand';

const initialState: LanguageState = {
  language: 'english',
};

export const createLanguageSlice: StateCreator<
  Store,
  [['zustand/immer', never], ['zustand/devtools', never]],
  [],
  LanguageSlice
> = (set) => ({
  ...initialState,
  setLanguage: (language: Language) => {
    // Apply language class to document root
    const root = document.documentElement;
    root.classList.forEach((cls) => {
      if (cls.startsWith('lang-')) root.classList.remove(cls);
    });
    root.classList.add(`lang-${language}`);

    // Save to localStorage
    localStorage.setItem('language', language);

    // Update store
    set(() => ({ language }));
  },
});
