import { type StateCreator } from 'zustand';

const initialFormData: JobFormData = {
  jobTitle: '',
  positionAppliedFor: '',
  location: '',
  minExperience: '',
  maxExperience: '',
  skills: [],
  startingDate: '',
  endingDate: '',
  description: '',
  interviewAgent: '',
  interviewDuration: '',
  totalAgents: 0,
  totalDuration: '0h',
};

const initialState: JobState = {
  currentStep: 1,
  formData: initialFormData,
};

export const createJobSlice: StateCreator<
  Store,
  [['zustand/immer', never], ['zustand/devtools', never]],
  [],
  JobSlice
> = (set, get) => ({
  ...initialState,
  setCurrentStep: (step: number) => set(() => ({ currentStep: step })),
  updateFormData: (data: Partial<JobFormData>) =>
    set((state) => ({
      formData: { ...state.formData, ...data },
    })),
  nextStep: () =>
    set(() => ({
      currentStep: Math.min(get().currentStep + 1, 3),
    })),
  prevStep: () =>
    set(() => ({
      currentStep: Math.max(get().currentStep - 1, 1),
    })),
  resetForm: () => set(() => ({ ...initialState })),
});
