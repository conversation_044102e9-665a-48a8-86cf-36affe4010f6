import { type StateCreator } from 'zustand';

const initialState: ThemeState = {
  theme: 'light',
};

export const createThemeSlice: StateCreator<
  Store,
  [['zustand/immer', never], ['zustand/devtools', never]],
  [],
  ThemeSlice
> = (set) => ({
  ...initialState,
  setTheme: (theme: Theme) => {
    // Apply theme to DOM
    const root = window.document.documentElement;
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    root.classList.remove('light', 'dark');
    const systemTheme = mediaQuery.matches ? 'dark' : 'light';
    const effectiveTheme = theme === 'system' ? systemTheme : theme;
    root.classList.add(effectiveTheme);

    // Save to localStorage
    localStorage.setItem(import.meta.env.VITE_UI_THEME || 'previa-ui-theme', theme);

    // Update store
    set(() => ({ theme }));
  },
});
