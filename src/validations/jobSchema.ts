import { z } from 'zod';

const today = new Date();
today.setHours(0, 0, 0, 0);

const jobInfoSchema = z
  .object({
    title: z.string().min(1, 'Title is required'),
    description: z.string().optional(),
    location: z.string().optional(),
    initial_filter_criteria: z.string().optional(),
    position_applied_for: z.string().min(1, 'Position is required'),
    min_exp: z.string().min(1, 'Minimum experience is required'),
    max_exp: z.string().min(1, 'Maximum experience is required'),
    skills: z
      .array(z.object({ label: z.string(), value: z.string() }))
      .min(1, 'You have to choose at least one skill'),
    starting_date: z
      .date({
        required_error: 'Date is required',
        invalid_type_error: 'Invalid date',
      })
      .min(today, 'Starting date must be today or in the future'),
    application_deadline: z
      .date({
        required_error: 'Date is required',
        invalid_type_error: 'Invalid date',
      })
      .min(today, 'Application deadline must be in the future'),
  })
  .refine(
    (data) => {
      if (data.min_exp !== undefined && data.max_exp !== undefined) {
        return data.max_exp > data.min_exp;
      }
      return true;
    },
    {
      message: 'Maximum experience must be greater than minimum experience',
      path: ['max_exp'],
    }
  )
  .refine(
    (data) => {
      if (data.starting_date && data.application_deadline) {
        return data.application_deadline > data.starting_date;
      }
      return true;
    },
    {
      message: 'Application deadline must be after starting date',
      path: ['application_deadline'],
    }
  );

export type JobInfoType = z.infer<typeof jobInfoSchema>;

export default jobInfoSchema;
