import { z } from 'zod';

const CreateCandidateSchema = z.object({
  full_name: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone_number: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  years_of_experience: z.union([
    z.coerce
      .string()
      .min(1, { message: 'Total Percentage is required.' })
      .transform((v) => Number(v)),
    z.literal('').refine(() => false, { message: 'Total Percentage is required.' }),
  ]),
  cv_link: z.string().min(1, 'CV is required'),
});

export type CreateCandidateType = z.infer<typeof CreateCandidateSchema>;

export default CreateCandidateSchema;
