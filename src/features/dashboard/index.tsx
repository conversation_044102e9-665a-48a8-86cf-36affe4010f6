'use client';

import { PageHeader } from '@/components/shared/PageHeader';
import {
  ActiveJobsTable,
  InterviewAgentsCard,
  JobCreationCard,
} from '@/features/dashboard/components';
import { motion } from 'framer-motion';

export default function DashboardPage() {
  return (
    <div className='bg-neutral-100 dark:bg-background flex h-full flex-col'>
      <PageHeader title='Dashboard' />

      <div className='flex-1 overflow-y-auto'>
        <div className='flex h-full flex-1 space-y-6 overflow-y-auto p-6'>
          <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <JobCreationCard />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <ActiveJobsTable />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <InterviewAgentsCard />
            </motion.div>

            {/* Empty cards */}
            <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className='border-muted-foreground/20 h-full rounded-lg border-2 bg-card'
              />
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
                className='border-muted-foreground/20 h-full rounded-lg border-2 bg-card'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
