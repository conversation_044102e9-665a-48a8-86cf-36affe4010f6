'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardT<PERSON>le,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui';

const agentData = [
  {
    title: 'HR L-1',
    usedIn: 7,
  },
  {
    title: 'Front end Developer L-2',
    usedIn: 4,
  },
  {
    title: 'Graphic Designer',
    usedIn: 4,
  },
];

export function InterviewAgentsCard() {
  return (
    <Card className='h-full'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle className='font-sf-pro-bold text-xl font-medium'>Top Interview Agents</CardTitle>
        <Button variant='secondary' size='sm' className='hover:bg-previa-primary-100 text-black'>
          View all
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow className='text-xs'>
              <TableHead className='text-previa-neutral-500'>Agent title</TableHead>
              <TableHead className='text-previa-neutral-500 text-right'>Used in</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {agentData.map((agent) => (
              <TableRow key={agent.title}>
                <TableCell className='border-b border-neutral-200 font-medium'>
                  {agent.title}
                </TableCell>
                <TableCell className='border-b border-neutral-200 text-right'>
                  {agent.usedIn}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
