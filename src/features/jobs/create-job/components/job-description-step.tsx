'use client';

import { JobDescriptionEditor } from './job-description-editor';
import HookFormItem from '@/components/hook-form/HookFormItem';
import { MultiSelect } from '@/components/select/multiple-selector';
import SimpleSelect from '@/components/select/simple-selector';
import {
  <PERSON>ton,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  DatePicker,
  Form,
  Input,
} from '@/components/ui';
import { useJobStore } from '@/features/jobs/create-job/hooks/useJobStore';
import { useCreateJobMutation } from '@/hooks/api/use-job';
import { roles, skills } from '@/utils/data';
import { getValuesFromOptions } from '@/utils/helper';
import jobInfoSchema, { type JobInfoType } from '@/validations/jobSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { forwardRef, useImperativeHandle, useState } from 'react';
import { useForm } from 'react-hook-form';

export const JobDescriptionStep = forwardRef<{ triggerValidation: () => Promise<boolean> }>(
  (_, ref) => {
    const allLabelClsName = '!text-foreground font-semibold tracking-wide';
    // const { formData: formDataStore, updateFormData, nextStep } = useJobStore();
    const { updateFormData } = useJobStore();
    const { mutate: createJobInformation, isPending } = useCreateJobMutation();
    const [showEditor, setShowEditor] = useState(false);
    const [editorMode, setEditorMode] = useState<'ai' | 'manual'>('manual');
    const form = useForm<JobInfoType>({
      resolver: zodResolver(jobInfoSchema),
      defaultValues: {
        title: '',
        description: '',
        position_applied_for: '',
        min_exp: '',
        max_exp: '',
        skills: [],
      },
    });

    useImperativeHandle(ref, () => ({
      triggerValidation: async () => {
        return await form.trigger();
      },
    }));

    const onSubmit = (data: JobInfoType, submitType: 'ai' | 'manual') => {
      const updateData: JobInformation = {
        ...data,
        skills: getValuesFromOptions(data.skills),
        min_exp: parseInt(data.min_exp),
        max_exp: parseInt(data.max_exp),
        location: data.location || 'Dhaka',
        starting_date: data.starting_date?.toISOString(),

        application_deadline: data.application_deadline?.toISOString(),
      };

      updateFormData(updateData);
      setEditorMode(submitType);
      createJobInformation(updateData, {
        onSuccess: () => {
          setShowEditor(true);
          // nextStep();
        },
        // onError is handled in the mutation hook
      });
    };

    const handleBackToForm = () => {
      setShowEditor(false);
    };

    if (showEditor) {
      return <JobDescriptionEditor mode={editorMode} onBack={handleBackToForm} />;
    }

    return (
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className='mx-auto max-w-2xl'
      >
        <Card className='border-none bg-transparent shadow-none'>
          <CardHeader className='text-center'>
            <CardTitle className='text-xl font-bold'>Define the Role</CardTitle>
            <CardDescription className='text-md text-muted-foreground mx-auto max-w-xl'>
              Create your job post your way. Write a prompt to let AI generate it for you, or build
              it manually by writing by your own
            </CardDescription>
          </CardHeader>
          <Form {...form}>
            <form>
              <CardContent className='bg-card z-[9999] space-y-6 rounded-2xl border border-neutral-200 p-4'>
                <div className='space-y-2'>
                  <HookFormItem name='title' label='Job title' labelClassName={allLabelClsName}>
                    <Input placeholder='e.g. Senior React Developer' />
                  </HookFormItem>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <HookFormItem
                      name='position_applied_for'
                      label='Position Applied For'
                      labelClassName={allLabelClsName}
                    >
                      <SimpleSelect
                        options={roles}
                        placeholder='Select job position'
                        name='position_applied_for'
                      />
                    </HookFormItem>
                  </div>

                  <div className='space-y-2'>
                    <HookFormItem name='location' label='Location' labelClassName={allLabelClsName}>
                      <Input placeholder='Enter location' />
                    </HookFormItem>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <HookFormItem
                      name='min_exp'
                      label='Min Experience (Years)'
                      labelClassName={allLabelClsName}
                    >
                      <Input />
                    </HookFormItem>
                  </div>

                  <div className='space-y-2'>
                    <HookFormItem
                      name='max_exp'
                      label='Max Experience (Years)'
                      labelClassName={allLabelClsName}
                    >
                      <Input />
                    </HookFormItem>
                  </div>
                </div>

                <div className='space-y-2'>
                  <HookFormItem name='skills' label='Skills' labelClassName={allLabelClsName}>
                    <MultiSelect options={skills} placeholder='Select skills' />
                  </HookFormItem>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <HookFormItem
                      name='starting_date'
                      label='Starting date'
                      className='flex flex-col'
                      labelClassName={allLabelClsName}
                    >
                      <DatePicker
                        key='start-date'
                        id='start-date'
                        onChange={(date) => {
                          if (date) form.setValue('starting_date', date);
                        }}
                      />
                    </HookFormItem>
                  </div>

                  <div className='space-y-2'>
                    <HookFormItem
                      name='application_deadline'
                      label='Ending date'
                      className='flex flex-col'
                      labelClassName={allLabelClsName}
                    >
                      <DatePicker
                        key='end-date'
                        id='end-date'
                        onChange={(date) => {
                          if (date) form.setValue('application_deadline', date);
                        }}
                      />
                    </HookFormItem>
                  </div>
                </div>

                <div className='flex w-full gap-3 pt-4'>
                  <div className='relative inline-block w-1/2'>
                    <div className='absolute -inset-0.5 overflow-hidden rounded-lg'>
                      <div className='animate-border-sweep absolute inset-0 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-80'></div>
                    </div>

                    <Button
                      type='submit'
                      name='submitTypeAi'
                      value='ai'
                      loading={isPending}
                      onClick={form.handleSubmit((data) => onSubmit(data, 'ai'))}
                      className='bg-logo-gradient relative w-full min-w-[250px] overflow-hidden border-0 transition-all before:absolute before:top-0 before:right-0 before:h-full before:w-12 before:translate-x-12 before:rotate-6 before:bg-white before:opacity-10 before:duration-700 hover:shadow-lg hover:shadow-purple-500/30 hover:before:-translate-x-80'
                    >
                      <span className='relative z-10'>Create Description with AI</span>
                    </Button>
                  </div>

                  <Button
                    variant='outline'
                    type='submit'
                    name='submitTypeManual'
                    value='manual'
                    onClick={form.handleSubmit((data) => onSubmit(data, 'manual'))}
                    loading={isPending}
                    className='w-1/2 hover:shadow-lg hover:shadow-neutral-400/30'
                  >
                    Create manually
                  </Button>
                </div>
              </CardContent>
            </form>
          </Form>
        </Card>
      </motion.div>
    );
  }
);
