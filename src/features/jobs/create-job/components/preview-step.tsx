'use client';

import { Button, Input, Label } from '@/components/ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useJobStore } from '@/features/jobs/create-job/hooks/useJobStore';
import { motion } from 'framer-motion';
import { Upload } from 'lucide-react';

export function PreviewStep() {
  const { prevStep } = useJobStore();

  const handleBack = () => {
    prevStep();
  };

  const handlePublish = () => {
    // Handle job publishing
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className='mx-auto max-w-4xl'
    >
      <div className='mb-6 text-center'>
        <h2 className='text-2xl font-bold'>Review and Test Before Publishing</h2>
        <p className='text-muted-foreground'>
          Review your job post as candidates will see it and test your selected AI interview agent
          using a sample or uploaded CV. Then you&apos;re ready to publish.
        </p>
      </div>

      <div className='mb-6 grid grid-cols-2 gap-6'>
        <Button variant='outline' className='h-auto bg-transparent p-4'>
          <div className='text-center'>
            <div className='font-medium'>Job Description</div>
          </div>
        </Button>
        <Button variant='outline' className='h-auto bg-transparent p-4'>
          <div className='text-center'>
            <div className='font-medium'>Interview Agent</div>
          </div>
        </Button>
      </div>

      {/* Sticky Job Header */}
      <div className='sticky top-0 z-10 rounded-t-lg border bg-gradient-to-r from-blue-100 to-blue-200 p-6'>
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Python Developer</h1>
            <p className='text-gray-600'>Mid-Level • Dhaka, Bangladesh</p>
          </div>
          <Button className='bg-blue-500 hover:bg-blue-600'>Apply Now</Button>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className='max-h-[600px] overflow-y-auto rounded-b-lg border-x border-b bg-white'>
        <div className='space-y-8 p-6'>
          {/* Role Overview */}
          <section>
            <h3 className='mb-4 text-xl font-semibold'>Role Overview</h3>
            <div className='space-y-4'>
              <div>
                <h4 className='mb-2 font-medium'>About the Role</h4>
                <p className='leading-relaxed text-gray-700'>
                  We are looking for a Product Manager (L-2) who will help shape the future of our
                  digital products by owning key initiatives, driving user-centered innovation, and
                  collaborating across cross-functional teams. This role is ideal for someone with
                  2-4 years of hands-on experience in product management who&apos;s ready to take
                  the next step in their career.
                </p>
              </div>

              <div>
                <h4 className='mb-2 font-medium'>Key Responsibilities</h4>
                <ul className='list-inside list-disc space-y-1 text-gray-700'>
                  <li>
                    Translate business objectives and user needs into clear product strategies and
                    roadmaps.
                  </li>
                  <li>
                    Work closely with design, engineering, marketing, and customer success teams to
                    deliver product features on time and with high quality.
                  </li>
                  <li>
                    Gather and prioritize product and customer requirements through market research,
                    user feedback, and competitive analysis.
                  </li>
                  <li>
                    Define clear product goals and success metrics; track and report on performance.
                  </li>
                </ul>
              </div>

              <div>
                <h4 className='mb-2 font-medium'>Requirements</h4>
                <ul className='list-inside list-disc space-y-1 text-gray-700'>
                  <li>2-4 years of experience in a Product Management or related role.</li>
                  <li>Strong understanding of Agile/Scrum methodologies.</li>
                  <li>
                    Experience working with cross-functional teams in a tech-driven environment.
                  </li>
                </ul>
              </div>

              <div>
                <h4 className='mb-2 font-medium'>Competitive salary</h4>
                <ul className='list-inside list-disc space-y-1 text-gray-700'>
                  <li>Flexible working hours</li>
                  <li>Friendly and collaborative team environment</li>
                  <li>Annual performance bonus</li>
                  <li>Learning & development support</li>
                  <li>Location: In-office at our Dhaka HQ (with occasional remote flexibility)</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Application Form */}
          <section>
            <h3 className='mb-4 text-xl font-semibold'>Application form</h3>
            <div className='space-y-4'>
              <div>
                <Label htmlFor='fullName'>Full name</Label>
                <Input id='fullName' placeholder='Placeholder' />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <Label htmlFor='email'>Email</Label>
                  <Input id='email' type='email' placeholder='Placeholder' />
                </div>
                <div>
                  <Label htmlFor='phone'>Phone</Label>
                  <Input id='phone' placeholder='Placeholder' />
                </div>
              </div>

              <div>
                <Label htmlFor='address'>Address</Label>
                <Input id='address' placeholder='Placeholder' />
              </div>

              <div>
                <Label htmlFor='experience'>Years of experience</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder='Placeholder' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='0-1'>0-1 years</SelectItem>
                    <SelectItem value='2-4'>2-4 years</SelectItem>
                    <SelectItem value='5-7'>5-7 years</SelectItem>
                    <SelectItem value='8+'>8+ years</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor='skills'>Skills</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder='Placeholder' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='python'>Python</SelectItem>
                    <SelectItem value='react'>React</SelectItem>
                    <SelectItem value='javascript'>JavaScript</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor='resume'>Resume/CV</Label>
                <div className='rounded-lg border-2 border-dashed border-gray-300 p-8 text-center'>
                  <Upload className='mx-auto mb-4 size-12 text-gray-400' />
                  <p className='text-gray-600'>
                    Drag your file or{' '}
                    <button className='text-blue-500 hover:underline'>browse</button>
                  </p>
                  <p className='mt-2 text-sm text-gray-500'>Max 5 MB files are allowed</p>
                  <p className='text-sm text-gray-500'>Only support PDF and DOC files</p>
                </div>
              </div>

              <Button className='w-full bg-blue-500 hover:bg-blue-600'>Submit application</Button>
            </div>
          </section>
        </div>
      </div>

      <div className='mt-6 flex justify-between'>
        <Button variant='outline' onClick={handleBack}>
          Back
        </Button>
        <div className='space-x-3'>
          <Button variant='outline'>Preview Interview Agents</Button>
          <Button onClick={handlePublish} className='bg-blue-500 hover:bg-blue-600'>
            Publish Job Post
          </Button>
        </div>
      </div>
    </motion.div>
  );
}
