import { useStore } from '@/stores/store';
import { useShallow } from 'zustand/react/shallow';

export const useJobStore = () => {
  return useStore(
    useShallow((state: JobSlice) => ({
      currentStep: state.currentStep,
      formData: state.formData,

      setCurrentStep: state.setCurrentStep,
      updateFormData: state.updateFormData,
      nextStep: state.nextStep,
      prevStep: state.prevStep,
      resetForm: state.resetForm,
    }))
  );
};
