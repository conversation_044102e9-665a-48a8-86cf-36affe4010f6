import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useState } from 'react';
import { InterviewSession } from '../candidate-interview/components/InterviewSession';
import { interviewSession } from '../candidate-interview/signals/interviewSignals';
import { timerService } from '../candidate-interview/services/timerService';
import { mediaService } from '../candidate-interview/services/mediaService';
import { useCreateInterviewSessionMutation } from '@/hooks/api/use-interview';

const Interview = () => {
  useSignals();
  const createSessionMutation = useCreateInterviewSessionMutation();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    // Initialize interview session and check media permissions
    const initializeInterview = async () => {
      setIsInitializing(true);
      
      try {
        // Check media permissions first
        await mediaService.checkExistingPermissions();
        
        // Create interview session with dummy data
        const sessionRequest = {
          candidateProfile: {
            name: '<PERSON>',
            position: 'Python Developer',
            company: 'Vivasoft Ltd'
          },
          totalDuration: 1800 // 30 minutes
        };

        const sessionResponse = await createSessionMutation.mutateAsync(sessionRequest);
        
        interviewSession.value = {
          interviewId: sessionResponse.interviewId,
          totalDuration: sessionResponse.totalDuration,
          candidateProfile: sessionResponse.candidateProfile,
          cvS3Key: 'dummy_cv_key_123', // Dummy CV S3 key
          difficulty: 'medium'
        };

        // Start the overall timer
        timerService.startOverallTimer(sessionResponse.totalDuration);
      } catch (error) {
        console.error('Failed to create interview session:', error);
        // Fallback to local session with dummy data
        interviewSession.value = {
          interviewId: 'interview_' + Date.now(),
          totalDuration: 1800,
          candidateProfile: {
            name: 'Alex Rahman',
            position: 'Python Developer',
            company: 'Vivasoft Ltd'
          },
          cvS3Key: 'dummy_cv_key_123',
          difficulty: 'medium'
        };
        timerService.startOverallTimer(1800);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeInterview();

    // Cleanup on unmount
    return () => {
      timerService.cleanup();
    };
  }, []);

  if (isInitializing || createSessionMutation.isPending) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing your interview session...</p>
          <p className="text-sm text-gray-500 mt-2">Checking camera and microphone permissions</p>
        </div>
      </div>
    );
  }

  return <InterviewSession />;
};

export default Interview;
