import { mediaState } from '../signals/interviewSignals';

export interface MediaDevice {
  deviceId: string;
  label: string;
  kind: 'videoinput' | 'audioinput';
}

class MediaService {
  // Check if media APIs are available
  isMediaSupported(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }

  // Check if we're in a secure context (HTTPS)
  isSecureContext(): boolean {
    return window.isSecureContext || location.protocol === 'https:';
  }

  // Check if permissions were previously granted
  async checkExistingPermissions(): Promise<boolean> {
    try {
      if (!this.isMediaSupported()) {
        return false;
      }

      // Try to get permission status
      const permissionStatus = await navigator.permissions.query({ name: 'camera' as PermissionName });
      const micPermissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      
      if (permissionStatus.state === 'granted' && micPermissionStatus.state === 'granted') {
        // Permissions are granted, try to get the stream
        return await this.restoreMediaStream();
      }
      
      return false;
    } catch (error) {
      // Fallback: try to get media stream directly
      console.log('Permission API not available, trying direct access', error);
      return await this.restoreMediaStream();
    }
  }

  // Restore media stream without showing permission prompt
  async restoreMediaStream(): Promise<boolean> {
    try {
      if (!this.isMediaSupported()) {
        return false;
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      mediaState.value = {
        ...mediaState.value,
        hasPermission: true,
        stream,
        error: ''
      };
      
      return true;
    } catch (error) {
      // Don't update error state here as this is a silent check
      console.log('No existing permissions found', error);
      return false;
    }
  }

  async requestPermissions(): Promise<boolean> {
    try {
      // Check if media APIs are supported
      if (!this.isMediaSupported()) {
        throw new Error('Media devices not supported in this browser');
      }

      // Check if we're in a secure context
      if (!this.isSecureContext()) {
        throw new Error('Media access requires HTTPS connection');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      mediaState.value = {
        ...mediaState.value,
        hasPermission: true,
        stream,
        error: ''
      };
      
      return true;
    } catch (error) {
      let errorMessage = 'Permission denied';
      
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = 'Camera and microphone access denied. Please allow permissions and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'No camera or microphone found on this device.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = 'Media devices not supported in this browser.';
        } else if (error.name === 'NotReadableError') {
          errorMessage = 'Camera or microphone is already in use by another application.';
        } else {
          errorMessage = error.message;
        }
      }
      
      mediaState.value = {
        ...mediaState.value,
        hasPermission: false,
        error: errorMessage
      };
      return false;
    }
  }

  async getAvailableDevices(): Promise<{ cameras: MediaDevice[], microphones: MediaDevice[] }> {
    try {
      if (!this.isMediaSupported()) {
        return { cameras: [], microphones: [] };
      }

      const devices = await navigator.mediaDevices.enumerateDevices();
      
      const cameras = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,
          kind: 'videoinput' as const
        }));

      const microphones = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
          kind: 'audioinput' as const
        }));

      return { cameras, microphones };
    } catch (error) {
      console.error('Failed to enumerate devices:', error);
      return { cameras: [], microphones: [] };
    }
  }

  async switchCamera(deviceId: string): Promise<boolean> {
    try {
      if (!this.isMediaSupported()) return false;

      // Stop current stream
      this.stopStream();

      const stream = await navigator.mediaDevices.getUserMedia({
        video: { deviceId: { exact: deviceId } },
        audio: true
      });

      mediaState.value = {
        ...mediaState.value,
        stream,
        hasPermission: true,
        error: ''
      };

      return true;
    } catch (error) {
      console.error('Failed to switch camera:', error);
      return false;
    }
  }

  async switchMicrophone(deviceId: string): Promise<boolean> {
    try {
      if (!this.isMediaSupported()) return false;

      // Stop current stream
      this.stopStream();

      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: { deviceId: { exact: deviceId } }
      });

      mediaState.value = {
        ...mediaState.value,
        stream,
        hasPermission: true,
        error: ''
      };

      return true;
    } catch (error) {
      console.error('Failed to switch microphone:', error);
      return false;
    }
  }

  stopStream() {
    if (mediaState.value.stream) {
      mediaState.value.stream.getTracks().forEach(track => track.stop());
      mediaState.value = {
        ...mediaState.value,
        stream: null
      };
    }
  }

  async startRecording(): Promise<MediaRecorder | null> {
    if (!mediaState.value.stream) return null;

    try {
      const mediaRecorder = new MediaRecorder(mediaState.value.stream);
      return mediaRecorder;
    } catch (error) {
      console.error('Failed to start recording:', error);
      return null;
    }
  }
}

export const mediaService = new MediaService();

