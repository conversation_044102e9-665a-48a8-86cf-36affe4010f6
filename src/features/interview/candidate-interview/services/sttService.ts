interface TranscriptionResponse {
  text: string;
}

class STTService {
  async transcribeAudio(audioBlob: Blob): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.webm');

      const endpoint = import.meta.env.VITE_PUBLIC_STT_ENDPOINT;
      if (!endpoint) {
        throw new Error('STT endpoint is not defined');
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`STT API error: ${response.status}`);
      }

      const result = (await response.json()) as TranscriptionResponse;
      return result.text || '';
    } catch (error) {
      console.error('STT transcription failed:', error);
      throw new Error('Failed to transcribe audio');
    }
  }
}

export const sttService = new STTService();
