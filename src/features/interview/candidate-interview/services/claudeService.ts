export interface QuestionRequest {
  role: string;
  session_id?: string | undefined;
  cv_s3_key?: string | undefined;
  difficulty: string;
  prompt_text: string;
  qa_array: {
    question: string;
    answer: string;
  }[];
  remaining_time?: number;
}

export interface QuestionResponse {
  data: string; // The question text itself
}

class ClaudeService {
  getCompletionMessage(candidateName: string): string {
    return `Thank you ${candidateName} for participating in this interview. Your responses have been recorded and our team will review them shortly. We appreciate the time you've taken to share your experience and insights with us. You should hear back from us within the next few business days regarding the next steps in the process.`;
  }

  // Helper method to format question request with new structure
  formatQuestionRequest(
    interviewSession: any,
    remainingTime: number,
    qaArray: { question: string; answer: string }[] = [],
    difficulty: string = 'medium'
  ): QuestionRequest {
    return {
      role: interviewSession.candidateProfile.position || 'Software Developer',
      session_id: interviewSession.interviewId,
      cv_s3_key: interviewSession.cvS3Key || undefined,
      difficulty: difficulty,
      prompt_text: `You are conducting an interview for a ${interviewSession.candidateProfile.position} position at ${interviewSession.candidateProfile.company}. Generate the next appropriate interview question.`,
      qa_array: qaArray,
      remaining_time: remainingTime,
    };
  }

  // Helper method to format answer submission
  formatAnswerSubmission(
    interviewId: string,
    questionId: string,
    answer: string,
    duration: number
  ) {
    return {
      interviewId,
      questionId,
      answer,
      duration,
      timestamp: new Date().toISOString(),
    };
  }
}

export const claudeService = new ClaudeService();
