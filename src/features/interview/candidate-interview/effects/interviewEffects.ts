import { effect } from '@preact/signals-react';
import { currentPhase, InterviewPhase, isTimeUp } from '../signals/timerSignals';
import { interviewProgress } from '../signals/interviewSignals';
import { timerService } from '../services/timerService';

export const setupInterviewEffects = () => {
  // Time up effect - handle completion when time runs out
  effect(() => {
    if (isTimeUp.value && currentPhase.value === InterviewPhase.ANSWERING) {
      // Let current answer finish, mark for completion
      interviewProgress.value = {
        ...interviewProgress.value,
        completionReason: 'time_up'
      };
    } else if (isTimeUp.value && currentPhase.value !== InterviewPhase.COMPLETED) {
      timerService.completeInterview('time_up');
    }
  });

  // Phase transition logging (for debugging)
  effect(() => {
    console.log('Phase changed to:', currentPhase.value);
  });
};