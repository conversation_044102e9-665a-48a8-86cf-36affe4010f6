import { signal, computed } from '@preact/signals-react';

export interface CandidateProfile {
  name: string;
  position: string;
  company: string;
}

export interface InterviewConfig {
  interviewId: string;
  totalDuration: number; // in seconds
  candidateProfile: CandidateProfile;
  cvS3Key?: string;
  difficulty: string;
}

export interface QAItem {
  question: string;
  answer: string;
}

// Core Interview State Signals
export const interviewSession = signal<InterviewConfig>({
  interviewId: '',
  totalDuration: 1800, // 30 minutes default
  candidateProfile: {
    name: '',
    position: '',
    company: ''
  },
  cvS3Key: undefined,
  difficulty: 'medium'
});

export const interviewProgress = signal({
  currentQuestionIndex: 0,
  totalQuestions: 0,
  isCompleted: false,
  completionReason: '' as 'time_up' | 'all_questions' | 'manual'
});

export const currentQuestion = signal({
  text: '',
  expectedDuration: 60,
  isLoading: false,
  difficulty: 'medium' as 'easy' | 'medium' | 'hard',
  questionId: ''
});

export const answerState = signal({
  currentAnswer: '',
  transcribedText: '',
  editedAnswer: '',
  isRecording: false,
  isTranscribing: false,
  recordingBlob: null as Blob | null
});

export const mediaState = signal({
  hasPermission: false,
  isVideoEnabled: true,
  isAudioEnabled: true,
  stream: null as MediaStream | null,
  error: ''
});

// Store Q&A history for API calls
export const qaHistory = signal<QAItem[]>([]);

// Computed values
export const isInterviewActive = computed(() => 
  !interviewProgress.value.isCompleted && 
  interviewSession.value.interviewId !== ''
);