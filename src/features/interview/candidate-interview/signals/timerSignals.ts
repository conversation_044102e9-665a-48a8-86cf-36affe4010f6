import { signal, computed } from '@preact/signals-react';

export enum InterviewPhase {
  SETUP = 'setup',
  LOADING_QUESTION = 'loading_question',
  READING_QUESTION = 'reading_question',
  THINKING = 'thinking',
  ANSWERING = 'answering',
  TRANSCRIBING = 'transcribing',
  EDITING = 'editing',
  SUBMITTING = 'submitting',
  COMPLETED = 'completed'
}

// Timer Signals
export const overallTimeRemaining = signal(1800); // 30 minutes in seconds
export const thinkingTimeRemaining = signal(30);
export const answerTimeRemaining = signal(60);
export const editTimeRemaining = signal(30);
export const currentPhase = signal<InterviewPhase>(InterviewPhase.SETUP);

// Computed signals for UI display
export const overallTimeFormatted = computed(() => {
  const minutes = Math.floor(overallTimeRemaining.value / 60);
  const seconds = overallTimeRemaining.value % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

export const isTimeUp = computed(() => overallTimeRemaining.value <= 0);

export const shouldAutoProgress = computed(() => {
  switch (currentPhase.value) {
    case InterviewPhase.THINKING:
      return thinkingTimeRemaining.value <= 0;
    case InterviewPhase.ANSWERING:
      return answerTimeRemaining.value <= 0;
    case InterviewPhase.EDITING:
      return editTimeRemaining.value <= 0;
    default:
      return false;
  }
});

export const canRequestNewQuestion = computed(() => 
  overallTimeRemaining.value > 0 && 
  currentPhase.value !== InterviewPhase.COMPLETED
);