import { useSignals } from '@preact/signals-react/runtime';
import { useEffect } from 'react';
import { thinkingTimeRemaining, currentPhase, InterviewPhase } from '../signals/timerSignals';
import { timerService } from '../services/timerService';

export function ThinkingTimer() {
  useSignals();

  // Register auto-submit callback for thinking phase
  useEffect(() => {
    const handleAutoSubmit = () => {
      handleReady();
    };

    timerService.registerAutoSubmitCallback('thinking', handleAutoSubmit);

    return () => {
      timerService.unregisterAutoSubmitCallback('thinking');
    };
  }, []);

  if (currentPhase.value !== InterviewPhase.THINKING) return null;

  const handleReady = () => {
    timerService.forceTransitionToAnswering();
  };

  return (
    <div className='rounded-lg h-full bg-yellow-50 p-4 shadow-sm'>
      <div className='text-center'>
        <h3 className='text-lg font-semibold text-gray-800 mb-2'>
          Take your time to think
        </h3>
        <div className='text-3xl font-bold text-yellow-600 mb-4'>
          {thinkingTimeRemaining.value}s
        </div>
        <button 
          onClick={handleReady}
          className='w-full rounded-md bg-blue-600 py-2.5 text-sm font-medium text-white transition hover:bg-blue-700'
        >
          Ready to Answer
        </button>
        <p className='text-xs text-gray-500 mt-2'>
          {thinkingTimeRemaining.value > 0 ? (
            <>Auto-starts in {thinkingTimeRemaining.value} seconds</>
          ) : (
            <>Starting answer phase...</>
          )}
        </p>
      </div>
    </div>
  );
}



