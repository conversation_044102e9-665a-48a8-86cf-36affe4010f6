import { claudeService } from '../services/claudeService';
import { speechService } from '../services/speechService';
import { timerService } from '../services/timerService';
import {
  currentQuestion,
  interviewSession,
  interviewProgress,
  qaHistory,
} from '../signals/interviewSignals';
import {
  currentPhase,
  InterviewPhase,
  canRequestNewQuestion,
  overallTimeRemaining,
} from '../signals/timerSignals';
import PreviaAvatar from '@/assets/icons/previa-avatar';
import { useGenerateQuestionMutation } from '@/hooks/api/use-interview';
import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useState } from 'react';

export function QuestionDisplay() {
  useSignals();
  const [isLoadingQuestion, setIsLoadingQuestion] = useState(false);

  const generateQuestionMutation = useGenerateQuestionMutation();

  useEffect(() => {
    if (currentPhase.value === InterviewPhase.LOADING_QUESTION && canRequestNewQuestion.value) {
      loadNextQuestion();
    }
  }, [currentPhase.value, canRequestNewQuestion.value]);

  useEffect(() => {
    if (currentPhase.value === InterviewPhase.READING_QUESTION && currentQuestion.value.text) {
      readQuestionAloud();
    }
  }, [currentPhase.value, currentQuestion.value.text]);

  const loadNextQuestion = async () => {
    if (!canRequestNewQuestion.value) {
      showCompletionMessage();
      return;
    }

    setIsLoadingQuestion(true);

    try {
      const questionRequest = claudeService.formatQuestionRequest(
        interviewSession.value,
        overallTimeRemaining.value,
        qaHistory.value,
        interviewSession.value.difficulty
      );

      const response = await generateQuestionMutation.mutateAsync(questionRequest);

      // Generate a unique question ID
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      currentQuestion.value = {
        text: response.data, // Response is just the question string
        expectedDuration: 60, // Default duration
        isLoading: false,
        difficulty: interviewSession.value.difficulty as 'easy' | 'medium' | 'hard',
        questionId: questionId,
      };

      currentPhase.value = InterviewPhase.READING_QUESTION;
    } catch (error) {
      console.error('Failed to load question:', error);
      // Show fallback question or error state
      currentQuestion.value = {
        ...currentQuestion.value,
        text: 'I apologize, but I encountered an issue generating your next question.',
        isLoading: false,
        questionId: `fallback_${Date.now()}`,
      };
      currentPhase.value = InterviewPhase.READING_QUESTION;
    } finally {
      setIsLoadingQuestion(false);
    }
  };

  const readQuestionAloud = async () => {
    try {
      await speechService.speakText(currentQuestion.value.text);
      // Start thinking timer when question reading is complete
      timerService.startThinkingTimer();
    } catch (error) {
      console.error('TTS failed:', error);
      // Skip to thinking phase even if TTS fails
      timerService.startThinkingTimer();
    }
  };

  const showCompletionMessage = () => {
    const completionMessage = claudeService.getCompletionMessage(
      interviewSession.value.candidateProfile.name
    );

    currentQuestion.value = {
      ...currentQuestion.value,
      text: completionMessage,
      isLoading: false,
    };
  };

  const getDisplayContent = () => {
    if (interviewProgress.value.isCompleted || !canRequestNewQuestion.value) {
      return {
        speaker: 'Previa',
        message:
          currentQuestion.value.text ||
          claudeService.getCompletionMessage(interviewSession.value.candidateProfile.name),
      };
    }

    if (
      isLoadingQuestion ||
      currentPhase.value === InterviewPhase.LOADING_QUESTION ||
      generateQuestionMutation.isPending
    ) {
      return {
        speaker: 'Previa',
        message: 'Generating your next question...',
      };
    }

    return {
      speaker: 'Previa',
      message: currentQuestion.value.text || 'Welcome to your interview!',
    };
  };

  const content = getDisplayContent();

  return (
    <div className='flex items-start gap-6'>
      {/* Avatar section */}
      <div className='flex-shrink-0'>
        <div className='border-primary flex size-28 flex-col items-center justify-center gap-5 rounded-2xl border bg-gray-50'>
          <PreviaAvatar className='size-10' />
          <div className='flex w-full items-center justify-around'>
            <span className='text-xs font-medium text-gray-700'>Previa</span>
            <div className='mt-1 flex animate-pulse space-x-1'>
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className='size-1 rounded-full bg-blue-400' />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Question content */}
      <div className='flex-1'>
        <div className='mb-4'>
          <p className='text-lg leading-relaxed font-medium text-gray-900'>{content.message}</p>
        </div>

        {currentPhase.value === InterviewPhase.READING_QUESTION && (
          <div className='flex items-center gap-2 text-sm text-blue-600'>
            <div className='animate-pulse'>🔊</div>
            <span>Reading question aloud...</span>
          </div>
        )}

        {generateQuestionMutation.isError && (
          <div className='mt-2 text-sm text-red-500'>Question generation failed</div>
        )}
      </div>
    </div>
  );
}
