import { useSignals } from '@preact/signals-react/runtime';
import { overallTimeFormatted, currentPhase } from '../signals/timerSignals';

export function InterviewTimer() {
  useSignals();

  return (
    <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 border">
      <div className="text-center">
        <div className="text-2xl font-bold text-gray-800">
          {overallTimeFormatted.value}
        </div>
        <div className="text-sm text-gray-600 capitalize">
          {currentPhase.value.replace('_', ' ')}
        </div>
      </div>
    </div>
  );
}