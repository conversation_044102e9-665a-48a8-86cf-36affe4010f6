import { useEffect, useRef, useState } from 'react';

interface AudioVisualizerProps {
  stream: MediaStream | null;
  isRecording: boolean;
}

export function AudioVisualizer({ stream, isRecording }: AudioVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!stream || !isRecording) {
      cleanup();
      setIsInitialized(false);
      return;
    }

    initializeAudioContext();

    return () => {
      cleanup();
    };
  }, [stream, isRecording]);

  const initializeAudioContext = async () => {
    try {
      if (!stream) return;

      // Create audio context
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // Resume context if suspended
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }

      // Create analyser
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;

      // Create source from stream
      const source = audioContext.createMediaStreamSource(stream);
      source.connect(analyser);

      // Store references
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      sourceRef.current = source;

      setIsInitialized(true);
      startVisualization();
    } catch (error) {
      console.error('Failed to initialize audio context:', error);
    }
  };

  const startVisualization = () => {
    if (!analyserRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const analyser = analyserRef.current;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      if (!isRecording || !analyser || !ctx) return;

      // Get frequency data
      analyser.getByteFrequencyData(dataArray);

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Calculate bar width
      const barWidth = canvas.width / bufferLength;

      for (let i = 0; i < bufferLength; i++) {
        const barHeight = (dataArray[i] / 255) * canvas.height;

        // Create gradient effect
        const gradient = ctx.createLinearGradient(0, canvas.height - barHeight, 0, canvas.height);
        gradient.addColorStop(0, '#60A5FA'); // blue-400
        gradient.addColorStop(1, '#3B82F6'); // blue-500

        ctx.fillStyle = gradient;
        ctx.fillRect(i * barWidth, canvas.height - barHeight, barWidth - 1, barHeight);
      }

      animationRef.current = requestAnimationFrame(draw);
    };

    draw();
  };

  const cleanup = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = undefined;
    }

    if (sourceRef.current) {
      sourceRef.current.disconnect();
      sourceRef.current = null;
    }

    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    analyserRef.current = null;
  };

  if (!isRecording || !stream) {
    return (
      <div className='flex h-16 w-full items-center justify-center rounded-lg border border-gray-200 bg-gray-50'>
        <div className='h-1 w-3/4 rounded-full bg-gray-300'></div>
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className='flex h-16 w-full items-center justify-center rounded-lg border border-gray-200 bg-gray-50'>
        <div className='text-sm text-gray-500'>Initializing audio...</div>
      </div>
    );
  }

  return (
    <div className='h-16 w-full overflow-hidden rounded-lg border border-gray-200 bg-gray-50'>
      <canvas ref={canvasRef} width={800} height={64} className='h-full w-full' />
    </div>
  );
}
