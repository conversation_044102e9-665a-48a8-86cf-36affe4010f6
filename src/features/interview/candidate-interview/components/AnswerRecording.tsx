import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useRef, useState } from 'react';
import { answerTimeRemaining, currentPhase, InterviewPhase } from '../signals/timerSignals';
import { answerState, mediaState } from '../signals/interviewSignals';
import { mediaService } from '../services/mediaService';
import { timerService } from '../services/timerService';
import { sttService } from '../services/sttService';
import { getFormatTime } from '@/utils/helper';
import { AudioVisualizer } from './AudioVisualizer';
import { ClockFading } from 'lucide-react';

export function AnswerRecording() {
  useSignals();
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [transcribedText, setTranscribedText] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const chunksRef = useRef<Blob[]>([]);

  // Auto-start recording when entering ANSWERING phase
  useEffect(() => {
    if (currentPhase.value === InterviewPhase.ANSWERING && !isRecording) {
      console.log("Auto-starting recording for answering phase");
      startRecording();
    }
  }, [currentPhase.value]);

  // Register auto-submit callback
  useEffect(() => {
    const handleAutoSubmit = () => {
      if (isRecording) {
        console.log("Auto-submit triggered, stopping recording");
        stopRecording();
      }
    };

    timerService.registerAutoSubmitCallback('answer', handleAutoSubmit);

    return () => {
      timerService.unregisterAutoSubmitCallback('answer');
    };
  }, [isRecording]);

  const startRecording = async () => {
    if(chunksRef.current) stopRecording();
    try {
      const recorder = await mediaService.startRecording();
      if (!recorder) {
        console.error("Failed to get media recorder");
        return;
      }

      chunksRef.current = [];
      setTranscribedText('');
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      recorder.onstop = async () => {
        console.log("Recording stopped, starting transcription");
        const blob = new Blob(chunksRef.current, { type: 'audio/webm' });
        
        // Start transcription immediately
        setIsTranscribing(true);
        try {
          const transcription = await sttService.transcribeAudio(blob);
          console.log("Transcription result:", transcription);
          setTranscribedText(transcription);
          
          answerState.value = {
            ...answerState.value,
            recordingBlob: blob,
            transcribedText: transcription,
            isRecording: false
          };
        } catch (error) {
          console.error('Transcription failed:', error);
          setTranscribedText('Transcription failed. Please try again.');
          
          answerState.value = {
            ...answerState.value,
            recordingBlob: blob,
            transcribedText: '',
            isRecording: false
          };
        } finally {
          setIsTranscribing(false);
          currentPhase.value = InterviewPhase.TRANSCRIBING;
        }
      };

      recorder.start();
      setMediaRecorder(recorder);
      setIsRecording(true);
      
      answerState.value = {
        ...answerState.value,
        isRecording: true
      };

      console.log("Recording started successfully");
    } catch (error) {
      console.error("Failed to start recording:", error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      console.log("Stopping recording manually");
      mediaRecorder.stop();
      setIsRecording(false);
      timerService.stopTimer('answer');
    }
  };

  const handleManualStop = () => {
    stopRecording();
  };

  if (currentPhase.value !== InterviewPhase.ANSWERING) return null;

  return (
    <div className='space-y-4'>
      {/* Header with timer */}
      <div className='flex items-center justify-between'>
        <h3 className='text-lg font-medium text-gray-900'>Your answer</h3>
        <div className='flex items-center gap-2 text-sm text-orange-600'>
          <ClockFading className="size-4" />
          <span>Time to Talk: {getFormatTime(answerTimeRemaining.value)}</span>
        </div>
      </div>
      
      {/* Recording status */}
      <div className='rounded-lg bg-red-50 p-4 border border-red-200'>
        <div className='flex items-center justify-center gap-3 mb-4'>
          <div className={`w-4 h-4 rounded-full ${
            isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-400'
          }`} />
          <span className='font-medium text-gray-900'>
            {isRecording ? 'Recording your voice...' : 'Preparing to record'}
          </span>
        </div>

        {/* Audio Visualizer */}
        <div className="mb-4">
          <AudioVisualizer 
            stream={mediaState.value.stream} 
            isRecording={isRecording} 
          />
        </div>

        {/* Live Transcription Display */}
        {(transcribedText || isTranscribing) && (
          <div className="mb-4 p-3 bg-white rounded-md border border-gray-200">
            <div className="text-xs text-gray-500 mb-1">Live Transcription:</div>
            {isTranscribing ? (
              <div className="flex items-center gap-2 text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">Transcribing...</span>
              </div>
            ) : (
              <p className="text-sm text-gray-800">{transcribedText}</p>
            )}
          </div>
        )}
        
        <button 
          onClick={handleManualStop}
          disabled={!isRecording}
          className='w-full rounded-md bg-blue-600 py-3 px-4 text-sm font-medium text-white transition hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center gap-2'
        >
          <span>⏹️</span>
          Stop Recording
        </button>
        
        {answerTimeRemaining.value > 0 && (
          <p className='text-xs text-gray-500 mt-2 text-center'>
            Auto-submits in {getFormatTime(answerTimeRemaining.value)}
          </p>
        )}
      </div>
    </div>
  );
}
