import { timerService } from '../services/timerService';
import { interviewProgress, isInterviewActive } from '../signals/interviewSignals';
import {
  currentPhase,
  InterviewPhase,
  shouldAutoProgress,
  isTimeUp,
} from '../signals/timerSignals';
import { AnswerRecording } from './AnswerRecording';
import { InterviewCompletion } from './InterviewCompletion';
import { InterviewTimer } from './InterviewTimer';
import { QuestionDisplay } from './QuestionDisplay';
import { ThinkingTimer } from './ThinkingTimer';
import { TranscriptionEditor } from './TranscriptionEditor';
import { VideoFeed } from './VideoFeed';
import { useSignals } from '@preact/signals-react/runtime';
import { useEffect } from 'react';

export function InterviewSession() {
  useSignals();

  // Auto-progression effect
  useEffect(() => {
    if (shouldAutoProgress.value) {
      handlePhaseTransition();
    }
  }, [shouldAutoProgress.value]);

  // Time up effect
  useEffect(() => {
    if (isTimeUp.value && currentPhase.value === InterviewPhase.ANSWERING) {
      // Let current answer finish, then complete
      return;
    } else if (isTimeUp.value) {
      timerService.completeInterview('time_up');
    }
  }, [isTimeUp.value]);

  // Start with loading first question
  useEffect(() => {
    if (isInterviewActive.value && currentPhase.value === InterviewPhase.SETUP) {
      currentPhase.value = InterviewPhase.LOADING_QUESTION;
    }
  }, [isInterviewActive.value]);

  const handlePhaseTransition = () => {
    switch (currentPhase.value) {
      case InterviewPhase.THINKING:
        currentPhase.value = InterviewPhase.ANSWERING;
        break;
      case InterviewPhase.ANSWERING:
        currentPhase.value = InterviewPhase.TRANSCRIBING;
        break;
      case InterviewPhase.EDITING:
        currentPhase.value = InterviewPhase.SUBMITTING;
        break;
    }
  };

  if (interviewProgress.value.isCompleted) {
    return <InterviewCompletion />;
  }

  return (
    <div className='px-11 py-3'>
      {/* Main content area */}
      <div className='flex flex-col gap-3'>
        {/* Question section */}
        <div className='bg-card-gradient mb-8 rounded-2xl px-8 py-4'>
          <QuestionDisplay />
        </div>

        <div className='grid grid-cols-1 gap-8 rounded-2xl bg-gray-50 p-4 md:grid-cols-2'>
          <div>
            <VideoFeed />
          </div>

          <div className='flex flex-col'>
            {currentPhase.value === InterviewPhase.THINKING && <ThinkingTimer />}
            {currentPhase.value === InterviewPhase.ANSWERING && <AnswerRecording />}
            {(currentPhase.value === InterviewPhase.TRANSCRIBING ||
              currentPhase.value === InterviewPhase.EDITING) && <TranscriptionEditor />}
          </div>
        </div>
      </div>

      <InterviewTimer />
    </div>
  );
}
