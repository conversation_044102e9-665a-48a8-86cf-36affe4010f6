import { claudeService } from '../services/claudeService';
import { speechService } from '../services/speechService';
import { timerService } from '../services/timerService';
import {
  answerState,
  interviewProgress,
  interviewSession,
  currentQuestion,
  qaHistory,
} from '../signals/interviewSignals';
import {
  editTimeRemaining,
  currentPhase,
  InterviewPhase,
  canRequestNewQuestion,
} from '../signals/timerSignals';
import { useSubmitAnswerMutation } from '@/hooks/api/use-interview';
import { useSignals } from '@preact/signals-react/runtime';
import { ClockFading } from 'lucide-react';
import { useEffect, useState } from 'react';

export function TranscriptionEditor() {
  useSignals();
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [answerStartTime] = useState(Date.now());

  const submitAnswerMutation = useSubmitAnswerMutation();

  useEffect(() => {
    if (currentPhase.value === InterviewPhase.TRANSCRIBING && !isTranscribing) {
      performTranscription();
    }
  }, [currentPhase.value]);

  // Register auto-submit callback for edit phase
  useEffect(() => {
    const handleAutoSubmit = () => {
      handleSubmit();
    };

    timerService.registerAutoSubmitCallback('edit', handleAutoSubmit);

    return () => {
      timerService.unregisterAutoSubmitCallback('edit');
    };
  }, []);

  const performTranscription = async () => {
    setIsTranscribing(true);
    answerState.value = {
      ...answerState.value,
      isTranscribing: true,
    };

    try {
      // Use the transcribed text from STT service if available
      let transcribedText = answerState.value.transcribedText;

      // If no transcription available, fallback to manual input
      if (!transcribedText && answerState.value.recordingBlob) {
        transcribedText = await speechService.transcribeAudio(answerState.value.recordingBlob);
      }

      answerState.value = {
        ...answerState.value,
        transcribedText,
        editedAnswer: transcribedText,
        isTranscribing: false,
      };

      currentPhase.value = InterviewPhase.EDITING;
      timerService.startEditTimer();
    } catch (error) {
      console.error('Transcription failed:', error);
      // Fallback to existing transcription or empty
      answerState.value = {
        ...answerState.value,
        transcribedText: answerState.value.transcribedText || '',
        editedAnswer: answerState.value.transcribedText || '',
        isTranscribing: false,
      };
      currentPhase.value = InterviewPhase.EDITING;
      timerService.startEditTimer();
    } finally {
      setIsTranscribing(false);
    }
  };

  const handleSubmit = async () => {
    const answerDuration = Math.floor((Date.now() - answerStartTime) / 1000);

    try {
      // Submit answer using React Query mutation
      const answerSubmission = claudeService.formatAnswerSubmission(
        interviewSession.value.interviewId,
        currentQuestion.value.questionId,
        answerState.value.editedAnswer,
        answerDuration
      );

      await submitAnswerMutation.mutateAsync(answerSubmission);

      // Add Q&A to history for next question generation
      qaHistory.value = [
        ...qaHistory.value,
        {
          question: currentQuestion.value.text,
          answer: answerState.value.editedAnswer,
        },
      ];

      console.log('Answer submitted successfully');
    } catch (error) {
      console.error('Failed to submit answer:', error);
      // Continue with the flow even if submission fails
    }

    // Reset answer state
    answerState.value = {
      currentAnswer: '',
      transcribedText: '',
      editedAnswer: '',
      isRecording: false,
      isTranscribing: false,
      recordingBlob: null,
    };

    // Check if interview should continue or complete
    if (canRequestNewQuestion.value && !interviewProgress.value.isCompleted) {
      currentPhase.value = InterviewPhase.LOADING_QUESTION;
    } else {
      timerService.completeInterview(interviewProgress.value.completionReason || 'time_up');
    }
  };

  if (currentPhase.value === InterviewPhase.TRANSCRIBING) {
    return (
      <div className='rounded-lg bg-blue-50 p-4 shadow-sm'>
        <div className='text-center'>
          <div className='mx-auto mb-3 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600'></div>
          <p className='font-medium text-blue-700'>Transcribing your answer...</p>
          <p className='mt-1 text-sm text-gray-600'>Please wait while we process your response</p>
        </div>
      </div>
    );
  }

  if (currentPhase.value === InterviewPhase.EDITING) {
    return (
      <div className='space-y-4'>
        {/* Header with timer */}
        <div className='flex items-center justify-between'>
          <h3 className='text-lg font-medium text-gray-900'>Your answer</h3>
          <div className='flex items-center gap-2 text-sm text-orange-600'>
            <ClockFading className='size-4' />
            <span>Time to Talk: 0:{editTimeRemaining.value.toString().padStart(2, '0')} min</span>
          </div>
        </div>

        {/* Answer textarea */}
        <textarea
          className='h-40 w-full resize-none rounded-md border border-gray-300 p-4 text-sm text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none'
          value={answerState.value.editedAnswer}
          onChange={(e) =>
            (answerState.value = {
              ...answerState.value,
              editedAnswer: e.target.value,
            })
          }
          placeholder='Type or edit your answer here...'
        />

        {/* Submit button */}
        <button
          onClick={handleSubmit}
          disabled={submitAnswerMutation.isPending}
          className='flex w-full items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-3 text-sm font-medium text-white transition hover:bg-blue-700 disabled:opacity-50'
        >
          {submitAnswerMutation.isPending ? (
            <>
              <div className='h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent'></div>
              Submitting...
            </>
          ) : (
            <>
              <span>✓</span>
              Submit my answer
            </>
          )}
        </button>

        {submitAnswerMutation.isError && (
          <p className='text-center text-sm text-red-500'>
            Failed to submit answer, but continuing interview
          </p>
        )}

        {editTimeRemaining.value > 0 && (
          <p className='text-center text-xs text-gray-500'>
            Auto-submits in {editTimeRemaining.value} seconds
          </p>
        )}
      </div>
    );
  }

  return null;
}
