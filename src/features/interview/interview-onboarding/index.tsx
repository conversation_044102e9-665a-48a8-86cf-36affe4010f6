import { AIIcon } from '@/assets';
import { useNavigate } from '@tanstack/react-router';

const InterviewOnboarding = () => {
  const navigate = useNavigate();
  return (
    <>
      <div className='flex p-6'>
        <div className='flex h-[calc(100vh-72px-24px-24px)] w-full rounded-2xl border'>
          <div
            className='w-2/3 flex-7/12 rounded-l-2xl'
            style={{
              backgroundImage: 'url(/images/previa-interview-onboarding.png)',
              backgroundSize: '100% 105%',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              boxShadow: 'rgb(255 255 255) 0 -161px 100px 0 inset',
            }}
          >
            <div className='flex h-full flex-col justify-end gap-6 p-14'>
              <AIIcon />
              <div className='flex flex-col gap-4'>
                <h1 className='text-3xl font-semibold text-black'>
                  Welcome,
                  <br />
                  You're Invited to an Interview
                </h1>
                <p className='text-xl text-neutral-600'>
                  This interview is part of your application for <strong> Python Developer</strong> at <strong>Vivasoft Ltd</strong>.
                </p>
              </div>
            </div>
          </div>
          <div className='w-1/3 flex-5/12'>
            <div className='flex min-h-[calc(100vh-72px-24px-24px)] items-center justify-center'>
              <div className='w-full space-y-6 px-10'>
                <h1 className='text-2xl font-semibold text-gray-900'>Your Interview Journey</h1>

                <p className='text-gray-700'>
                  This interview is structured into <strong>three</strong> stages, and you'll need
                  to complete each one to move on to the next. Here's what to expect:
                </p>

                <div className='space-y-1 text-gray-800'>
                  <p>
                    <strong>Stage 1:</strong> HR Introduction
                  </p>
                  <p>
                    <strong>Stage 2:</strong> Technical Evaluation
                  </p>
                  <p>
                    <strong>Stage 3:</strong> Final Assessment
                  </p>
                </div>

                <p className='text-gray-700'>
                  You'll have 30 seconds to think, then 1 minute to answer.
                </p>

                <div className='rounded-md border border-orange-300 bg-orange-100 p-4 text-sm text-orange-800'>
                  <p>
                    <strong>Before You Begin,</strong> To start this interview, you'll need to
                    enable your <strong>webcam and microphone</strong>. You’ll be speaking your
                    answers aloud. we’ll transcribe your responses for review.
                  </p>
                </div>

                <button className='w-full rounded-md bg-blue-600 py-2.5 text-sm font-medium text-white hover:bg-blue-700'
                onClick={
                  () => {
                    navigate({ to: '/interview-onboarding-system-setup' });
                  }
                }>
                  Start Interview Setup
                </button>

                <p className='text-center text-sm text-gray-600'>
                  Need any help? Please{' '}
                  <a href='#' className='text-blue-600 underline'>
                    contact support
                  </a>{' '}
                  for assistance
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default InterviewOnboarding;
