import { Button } from '@/components/ui';

const JobInformation = () => {
  const jobDescription = `<article><h2>About the Role</h2><p>We are looking for a passionate <strong>Frontend Engineer</strong> to build responsive, accessible, and high-performance web applications using modern technologies like React, TypeScript, and Tailwind CSS. You will collaborate with designers, backend engineers, and product managers to ship delightful user experiences at speed and scale.</p><h2>What You’ll Do</h2><ul><li>Implement new product features and UI components in <strong>React + TypeScript</strong>.</li><li>Translate Figma designs into pixel-perfect, accessible (WCAG 2.1 AA) interfaces.</li><li>Own performance: measure, profile, and optimize Core Web Vitals.</li><li>Write clean, testable code with unit and integration tests.</li><li>Collaborate via PR reviews, RFCs, and design discussions.</li><li>Contribute to our internal component library and design system.</li></ul><h2>What We’re Looking For</h2><ul><li><strong>2–5 years</strong> of professional frontend experience.</li><li>Strong knowledge of <strong>React</strong>, <strong>TypeScript</strong>, and state management (e.g., Redux/Zustand).</li><li>Hands-on experience with <strong>Tailwind CSS</strong> or utility-first CSS approaches.</li><li>Solid grasp of <em>semantic HTML</em>, <em>ARIA</em>, and cross-browser quirks.</li><li>Understanding of build tools (Vite/Turbopack), REST/GraphQL, and CI/CD.</li><li>Ability to break down problems and communicate clearly in a team setting.</li></ul><h3>Nice to Have</h3><ul><li>Experience with Next.js (App Router), SSR/SSG, and edge rendering.</li><li>Familiarity with testing tools like Vitest/Jest and Playwright.</li><li>Design system experience (Storybook, tokens, theming).</li></ul><h2>Benefits</h2><ul><li>Competitive salary and performance bonus.</li><li>Flexible hours and hybrid work policy.</li><li>Learning budget for courses, books, and conferences.</li><li>Health insurance and paid time off.</li></ul><h2>How to Apply</h2><p>Submit your resume and links to your <strong>GitHub</strong> or <strong>portfolio</strong>. Shortlisted candidates will complete a brief take-home or live pairing exercise.</p><hr><p><small><strong>Equal Opportunity:</strong> We value diversity and provide an inclusive environment for all employees.</small></p></article>`;

  return (
    <div>
      <div className='relative h-50'>
        <div
          className='h-full bg-cover bg-center bg-no-repeat'
          style={{ backgroundImage: 'url(/images/application-banner.png)' }}
        >
          <div className='mx-auto flex h-full max-w-200 items-center justify-between'>
            <div>
              <h3 className='text-[40px] font-semibold text-black'>Python Developer</h3>
              <div className='text-gray-dark mt-1 flex items-center gap-2 text-sm'>
                <p>Mid-Level</p>
                <p className='bg-gray-dark block size-0.75 rounded-full' />
                <p>Dhaka,Bangladesh</p>
              </div>
            </div>

            <Button>Apply Now</Button>
          </div>
        </div>
      </div>

      <div className='mx-auto max-w-200'>
        <h3 className='text-gray-dark mt-10 mb-4 text-2xl'>Role Overview</h3>
        <div className='ql-editor text-gray-dark -ml-3.5'>
          <div dangerouslySetInnerHTML={{ __html: jobDescription }} />
        </div>
      </div>
    </div>
  );
};
export default JobInformation;
