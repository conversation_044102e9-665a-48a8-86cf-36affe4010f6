import FileUploader from '@/components/file-uploader/FileUploader';
import axios, { type AxiosError, type AxiosResponse } from 'axios';
import React from 'react';
import { toast } from 'sonner';

interface Props {
  setCV: React.Dispatch<React.SetStateAction<string | null>>;
}

const ResumeUploader = ({ setCV }: Props) => {
  // TODO: handling loading state during upload
  const handleUploadCV = async (files: File[]) => {
    if (files.length === 0) toast.error('Please select a file to upload');
    try {
      const formData = new FormData();
      formData.append('cv', files[0]);
      const response: AxiosResponse<{ cv_link: string }> = await axios.post(
        '/api/v1/candidates/upload-cv',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      setCV(response.data?.cv_link);
      toast.success('CV uploaded successfully');
    } catch (err) {
      toast.error(
        (err as AxiosError<{ message: string }>)?.response?.data?.message ?? 'Failed to upload cv'
      );
    }
  };

  return (
    <div>
      <FileUploader
        placeholder='Drag your file or'
        placeHolder2='browse'
        supportedFormats='Max 5 MB files are allowed'
        acceptedFileTypes={{ 'application/pdf': ['.pdf'], 'application/msword': ['.doc'] }}
        onFilesUploaded={(files) => handleUploadCV(files)}
      />
    </div>
  );
};

export default ResumeUploader;
