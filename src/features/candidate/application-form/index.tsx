import ResumeUploader from './components/resumeUploader';
import HookFormItem from '@/components/hook-form/HookFormItem';
import { Button, Form, FormLabel, Input } from '@/components/ui';
import CreateCandidateSchema, { CreateCandidateType } from '@/validations/candidateSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

const JobApplicationForm = () => {
  const [, setCv] = useState<string | null>('');

  const form = useForm<CreateCandidateType>({
    resolver: zodResolver(CreateCandidateSchema),
    defaultValues: {
      full_name: '',
      email: '',
      phone_number: '',
      address: '',
      years_of_experience: '',
      cv_link: '',
    },
  });

  const handleApplicationSubmit = (data: CreateCandidateType) => {
    console.log(data);
  };

  return (
    <div>
      {/* <JobInformation /> */}
      <section className='mx-auto my-10 max-w-200'>
        <h3 className='text-gray-dark mb-6 text-2xl'>Application Form</h3>
        <Form {...form}>
          <form className='w-full space-y-5' onSubmit={form.handleSubmit(handleApplicationSubmit)}>
            <HookFormItem name='full_name' label='Full Name' isRequired>
              <Input placeholder='Enter your full name' />
            </HookFormItem>

            <div className='grid grid-cols-2 gap-4'>
              <HookFormItem name='email' label='Email' isRequired>
                <Input placeholder='Enter your business email' />
              </HookFormItem>
              <HookFormItem name='phone_number' label='Phone' isRequired>
                <Input placeholder='Enter your phone number' />
              </HookFormItem>
            </div>

            <HookFormItem name='address' label='Address' isRequired>
              <Input placeholder='Enter your address' />
            </HookFormItem>

            <HookFormItem name='years_of_experience' label='Years of experience' isRequired>
              <Input placeholder='Enter your years of experience' />
            </HookFormItem>

            <div>
              <FormLabel className='mb-3'>Resume/CV</FormLabel>
              <ResumeUploader setCV={setCv} />
              <p className='text-gray-light mt-3 text-sm'>Only support PDF and DOC files</p>
            </div>

            <Button>Submit application</Button>
          </form>
        </Form>
      </section>
    </div>
  );
};
export default JobApplicationForm;
